<template>
  <div class="home-container">
    <!-- 统一吸顶容器 -->
    <div class="sticky-header" :class="{
      'sticky-header-scrolled': isScrolled,
      'sticky-header-no-banner': !hasBannerData
    }" :style="getStickyHeaderStyle()">
      <!-- 搜索栏 -->
      <div class="search-input" @click="goToSearch">
        <van-icon name="search" />
        <span class="placeholder">请输入活动名称或编号进行搜索</span>
      </div>

      <!-- 活动类型区域 -->
      <div class="category-container" v-if="isScrolled">
        <div class="category-scroll" :class="{ 'can-scroll': allCategories.length > 4 }">
          <!-- 全部活动 - 固定第一位 -->
          <div
            class="category-item"
            :class="{ active: activeCategory === 'all' }"
            @click="handleCategoryChange('all')"
          >
            <div class="category-icon-wrapper">
              <div class="category-icon all-icon">
                <img src="@/assets/images/allType.png" alt="全部活动" class="all-type-image" />
              </div>
            </div>
            <span class="category-name">全部活动</span>
          </div>

          <!-- 其他活动类型 - 按排序升序展示 -->
          <div
            v-for="category in sortedCategories"
            :key="category.id"
            class="category-item"
            :class="{ active: activeCategory === category.id }"
            @click="handleCategoryChange(category.id)"
          >
            <div class="category-icon-wrapper">
              <div class="category-icon">
                <img :src="category.icon" :alt="category.name" />
              </div>
            </div>
            <span class="category-name">{{ category.name }}</span>
          </div>
        </div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section" v-if="isScrolled">
        <button
          class="sort-btn left-btn"
          :class="{ active: sortType === 'time' }"
          @click="handleSort('time')"
        >
          最近发布
        </button>
        <div class="right-buttons">
          <button
            class="sort-btn popularity-btn"
            :class="{ active: sortType === 'popularity' }"
            @click="handleSort('popularity')"
          >
            人气排行
            <van-icon :name="popularityOrder === 'asc' ? 'arrow-up' : 'arrow-down'" v-if="sortType === 'popularity'" />
          </button>
          <button
            class="filter-btn"
            :class="{ active: hasActiveFilter || showFilterPopup }"
            @click="toggleFilter"
          >
            筛选
            <van-icon :name="showFilterPopup ? 'arrow-up' : 'arrow-down'" />
          </button>
        </div>
      </div>

      <!-- 筛选内容 -->
      <div v-if="showFilterPopup" class="filter-popup">
        <div class="filter-content">
          <!-- 活动类型筛选 -->
          <div class="filter-group">
            <h4 class="filter-title">活动类型</h4>
            <div class="filter-options">
              <button 
                v-for="category in allCategories" 
                :key="category.id"
                class="filter-option-btn"
                :class="{ active: filterForm.categoryId === category.id }"
                @click="filterForm.categoryId = category.id"
              >
                {{ category.name }}
              </button>
            </div>
          </div>

          <!-- 活动状态筛选 -->
          <div class="filter-group">
            <h4 class="filter-title">活动状态</h4>
            <div class="filter-options">
              <button 
                v-for="status in activityStatusOptions" 
                :key="status.value"
                class="filter-option-btn"
                :class="{ active: filterForm.activityStatus === status.value }"
                @click="filterForm.activityStatus = status.value"
              >
                {{ status.label }}
              </button>
            </div>
          </div>

          <!-- 报名状态筛选 -->
          <div class="filter-group">
            <h4 class="filter-title">活动报名状态</h4>
            <div class="filter-options">
              <button 
                v-for="status in registrationStatusOptions" 
                :key="status.value"
                class="filter-option-btn"
                :class="{ active: filterForm.registrationStatus === status.value }"
                @click="filterForm.registrationStatus = status.value"
              >
                {{ status.label }}
              </button>
            </div>
          </div>
        </div>

        <!-- 筛选操作按钮 -->
        <div class="filter-footer">
          <button class="filter-reset-btn" @click="resetFilter">重置</button>
          <button class="filter-confirm-btn" @click="applyFilter">确定</button>
        </div>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="content" @scroll="handleScroll" ref="content">
      <!-- Banner轮播 -->
      <div class="banner-section" v-if="bannerList && bannerList.length > 0">
        <template v-if="bannerList.length > 1">
          <!-- 使用安全的轮播组件 -->
          <div class="swipe-wrapper">
            <SafeSwipe
              ref="bannerSwipe"
              class="banner-swipe"
              :key="`banner-${bannerList.length}`"
              :autoplay="3000"
              :loop="true"
              indicator-color="white"
              :show-indicators="bannerList.length > 1"
              :touchable="bannerList.length > 1"
              :lazy-render="false"
              :swipe-threshold="0"
              loading-text="轮播图加载中..."
              @change="onBannerChange"
              @error="handleSwipeError"
              @ready="onSwipeReady"
            >
              <van-swipe-item v-for="(banner, index) in bannerList" :key="`banner-item-${index}`" @click="handleBannerClick(banner)">
                <img
                  :src="banner.imageUrl"
                  :alt="banner.title"
                  class="banner-image"
                  style="width: 100%; height: 100%; object-fit: cover;"
                  @load="onBannerImageLoad"
                  @error="onBannerImageError"
                />
              </van-swipe-item>
            </SafeSwipe>
          </div>
        </template>
        <template v-else>
          <div class="banner-single">
            <img
              :src="bannerList[0].imageUrl"
              :alt="bannerList[0].title"
              class="banner-image"
              @click="handleBannerClick(bannerList[0])"
              style="width: 100%; height: 100%; object-fit: cover;"
              @load="onBannerImageLoad"
              @error="onBannerImageError"
            />
          </div>
        </template>
      </div>

      <!-- 精选活动 -->
      <div class="featured-section" v-if="featuredList.length > 0">
        <h3 class="section-title">精选活动</h3>
        <div class="featured-grid">
          <div 
            v-for="(item, index) in featuredList.slice(0, 4)" 
            :key="index"
            class="featured-item"
            :class="`featured-item-${index + 1}`"
            @click="handleFeaturedClick(item)"
          >
            <div class="featured-icon">
              <img :src="item.imageUrl" :alt="item.title" class="featured-image" />
            </div>
            <div class="featured-content">
              <h4 class="featured-title">{{ item.title }}</h4>
              <p class="featured-subtitle">{{ item.subtitle }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动类型 -->
      <div v-if="categoryList && categoryList.length > 0">
        <div
          ref="categorySection"
          class="category-section"
        >
          <div class="category-container">
            <div class="category-scroll" :class="{ 'can-scroll': allCategories.length > 4 }">
              <!-- 全部活动 - 固定第一位 -->
              <div
                class="category-item"
                :class="{ active: activeCategory === 'all' }"
                @click="handleCategoryChange('all')"
              >
                <div class="category-icon-wrapper">
                  <div class="category-icon all-icon">
                    <img src="@/assets/images/allType.png" alt="全部活动" class="all-type-image" />
                  </div>
                </div>
                <span class="category-name">全部活动</span>
              </div>

              <!-- 其他活动类型 - 按排序升序展示 -->
              <div
                v-for="category in sortedCategories"
                :key="category.id"
                class="category-item"
                :class="{ active: activeCategory === category.id }"
                @click="handleCategoryChange(category.id)"
              >
                <div class="category-icon-wrapper">
                  <div class="category-icon">
                    <img :src="category.icon" :alt="category.name" />
                  </div>
                </div>
                <span class="category-name">{{ category.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 排序和筛选 -->
      <div
        class="filter-section"

        v-if="!isScrolled"
      >
        <button
          class="sort-btn left-btn"
          :class="{ active: sortType === 'time' }"
          @click="handleSort('time')"
        >
          最近发布
        </button>
        <div class="right-buttons">
          <button
            class="sort-btn popularity-btn"
            :class="{ active: sortType === 'popularity' }"
            @click="handleSort('popularity')"
          >
            人气排行
            <van-icon :name="popularityOrder === 'asc' ? 'arrow-up' : 'arrow-down'" v-if="sortType === 'popularity'" />
          </button>
          <button
            class="filter-btn"
            :class="{ active: hasActiveFilter || showFilterPopup }"
            @click="toggleFilter"
          >
            筛选
            <van-icon :name="showFilterPopup ? 'arrow-up' : 'arrow-down'" />
          </button>
        </div>
      </div>

      <!-- 活动列表 + 下拉刷新 -->
      <div class="activity-list">
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
            ref="activityListRef"
            :loading="loading"
            :finished="finished"
            :immediate-check="false"
            finished-text="—我也是有底线的—"
            loading-text="加载中..."
            @load="loadActivityList"
            v-if="categoryList && categoryList.length > 0"
          >
          <div
            v-for="activity in activityList"
            :key="activity.id"
            class="activity-item"
            @click="goToActivityDetail(activity)"
          >
            <!-- 活动头图 -->
            <div class="activity-image-wrapper">
              <img :src="activity.imageUrl" :alt="activity.title" class="activity-image" />
              <img src="@/assets/images/bar.png" class="activity-code-bg" />
              <div class="activity-code">活动编号：{{ activity.id }}</div>
            </div>

            <!-- 活动标题和信息区域 -->
            <div class="activity-content-wrapper">
              <!-- 活动标题 -->
              <h4 class="activity-title">{{ activity.title }}</h4>

              <!-- 活动信息 -->
              <div class="activity-info-section">
                <div class="info-row">
                  <span class="info-label">活动状态：</span>
                  <span class="activity-status" :class="activity.statusClass">{{ activity.statusText }}</span>
                </div>
                <div class="info-row">
                  <span class="info-label">报名时间：</span>
                  <span class="info-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                </div>
                <div class="info-row">
                  <span class="info-label">活动时间：</span>
                  <span class="info-value">yyyy/mm/dd HH:mm - yyyy/mm/dd HH:mm</span>
                </div>
                <div class="info-row">
                  <span class="info-label">报名人数：</span>
                  <span class="info-value">{{ activity.participantCount }}/{{ getMaxParticipantsText(activity.maxParticipants) }} 人</span>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="activity-actions" v-if="shouldShowButton(activity)">
                <button
                  class="action-button"
                  :class="getButtonClass(activity)"
                  @click.stop="handleActivityAction(activity)"
                >
                  {{ getButtonText(activity) }}
                </button>
              </div>
            </div>
          </div>
        </van-list>
        <!-- <div v-if="!loading && activityList.length === 0" style="text-align:center;color:#999;padding:16px 0 80px 0;">
          暂无数据
        </div> -->
        </van-pull-refresh>
      </div>
    </div>

  </div>
</template>

<script>
import Vue from 'vue';
import {
  Swipe,
  SwipeItem,
  List,
  Button,
  Popup,
  Icon,
  PullRefresh
} from 'vant';
import SafeSwipe from '@/components/SafeSwipe.vue';

Vue.use(Swipe)
  .use(SwipeItem)
  .use(List)
  .use(Button)
  .use(Popup)
  .use(Icon)
  .use(PullRefresh);

export default {
  name: 'Home',
  components: {
    SafeSwipe
  },
  data() {
    return {
      isScrolled: false,
      scrollOpacity: 0, // 滚动透明度
      bannerList: [],
      bannerSwipeReady: false, // banner轮播组件是否准备就绪
      containerReady: false, // 容器是否准备就绪
      bannerImagesLoaded: 0, // 已加载的banner图片数量
      featuredList: [],
      categoryList: [],
      activityList: [],
      activeCategory: 'all',
      sortType: 'time', // 'time' | 'popularity'
      popularityOrder: 'desc', // 'asc' | 'desc'
      loading: false,
      finished: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,
      refreshing: false,
      showFilterPopup: false,
      filterForm: {
        categoryId: 'all',
        activityStatus: 'all',
        registrationStatus: 'all'
      },
      // 添加数据加载状态管理
      isDataLoaded: false, // 数据是否已加载
      isInitialized: false, // 组件是否已初始化
      activityStatusOptions: [
        { label: '全部', value: 'all' },
        { label: '活动未开始', value: 'not_started' },
        { label: '活动进行中', value: 'ongoing' },
        { label: '活动已结束', value: 'ended' }
      ],
      registrationStatusOptions: [
        { label: '全部', value: 'all' },
        { label: '报名未开始', value: 'not_started' },
        { label: '报名进行中', value: 'ongoing' },
        { label: '报名已结束', value: 'ended' }
      ],

      // 错误处理相关
      errorRetryCount: 0, // 错误重试次数
      maxRetryCount: 3, // 最大重试次数
      lastErrorTime: 0, // 上次错误时间
      lastGlobalErrorTime: 0 // 上次全局错误时间
    };
  },
  computed: {

    // 按排序升序排列的活动类型
    sortedCategories() {
      return [...this.categoryList].sort((a, b) => (a.sort || 0) - (b.sort || 0));
    },

    // 所有活动类型（包括"全部"）
    allCategories() {
      return [{ id: 'all', name: '全部活动' }, ...this.sortedCategories];
    },
    hasActiveFilter() {
      return this.filterForm.categoryId !== 'all' ||
             this.filterForm.activityStatus !== 'all' ||
             this.filterForm.registrationStatus !== 'all';
    },
    hasBannerData() {
      return this.bannerList && this.bannerList.length > 0;
    },
    tabLineStyle() {
      // 计算当前激活 tab 的位置来设置下划线位置
      const allTabs = [{ id: 'all' }, ...this.categoryList];
      const activeIndex = allTabs.findIndex(tab => tab.id === this.activeCategory);

      if (activeIndex === -1) return { display: 'none' };

      // 每个 tab 的宽度大约是容器宽度除以可见 tab 数量
      const tabWidth = 100 / Math.min(allTabs.length, 4); // 最多显示4个tab
      const left = activeIndex * tabWidth;

      return {
        left: `${left}%`,
        width: `${tabWidth}%`,
        transform: 'translateX(0)',
        transition: 'all 0.3s ease'
      };
    }
  },
  watch: {
    bannerList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log('bannerList数据已加载，轮播组件将自动初始化');
          this.bannerImagesLoaded = 0;
          // SafeSwipe组件会自动处理初始化，无需手动处理
        }
      },
      immediate: false
    },
    categoryList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          console.log('categoryList数据已加载');
          // 移除自动触发列表加载，避免重复调用
          // 活动列表已经在initPage中加载了
        }
      },
      immediate: false
    }
  },
  mounted() {
    console.log('Home组件已挂载');
    // 只有在非缓存激活时才初始化
    if (!this.isInitialized) {
      this.initPage();
      this.isInitialized = true;
    }
    if (this.$refs.content) {
      this.bindScrollEvent();
    }



    // 添加轮播组件错误处理
    this.errorHandler = (event) => {
      if (event.message && (
        event.message.includes('Cannot read properties of null (reading \'width\')') ||
        event.message.includes('Cannot read properties of undefined (reading \'width\')') ||
        event.message.includes('Cannot read properties of null (reading \'offsetWidth\')') ||
        event.message.includes('Cannot read properties of undefined (reading \'offsetWidth\')') ||
        event.message.includes('minOffset') ||
        event.message.includes('move') ||
        event.message.includes('swipe')
      )) {
        console.log('检测到轮播组件错误，尝试重新初始化');
        console.log('错误详情:', event.message, event.filename, event.lineno);

        // 防止错误处理过于频繁
        const now = Date.now();
        if (!this.lastGlobalErrorTime || now - this.lastGlobalErrorTime > 2000) {
          this.lastGlobalErrorTime = now;
          this.handleSwipeError(event);
        }
      }
    };
    window.addEventListener('error', this.errorHandler);
    
    // 添加未捕获的Promise错误处理
    this.unhandledRejectionHandler = (event) => {
      if (event.reason && event.reason.message && (
        event.reason.message.includes('width') ||
        event.reason.message.includes('offsetWidth') ||
        event.reason.message.includes('minOffset')
      )) {
        console.log('检测到Promise轮播组件错误，尝试重新初始化');
        this.handleSwipeError();
      }
    };
    window.addEventListener('unhandledrejection', this.unhandledRejectionHandler);

    // 备用方案：确保活动列表能够加载（已移除，避免重复调用）
    // setTimeout(() => {
    //   if (this.activityList.length === 0 && !this.loading) {
    //     console.log('备用方案：直接调用loadActivityList');
    //     this.loadActivityList();
    //   }
    // }, 1000);
  },
  
  // Keep-alive 缓存组件激活时调用
  activated() {
    console.log('首页组件被激活 (activated)');
    console.log('当前状态 - isDataLoaded:', this.isDataLoaded, 'activityList.length:', this.activityList.length, 'bannerList.length:', this.bannerList.length);
    // 只有在数据完全为空时才重新加载
    if (!this.isDataLoaded || (this.activityList.length === 0 && this.bannerList.length === 0 && this.categoryList.length === 0)) {
      console.log('数据未加载或完全为空，重新初始化页面');
      this.initPage();
    } else {
      console.log('数据已加载，跳过重新初始化');
    }
  },
  
  // Keep-alive 缓存组件失活时调用
  deactivated() {
    console.log('首页组件被失活 (deactivated)');
    // 可以在这里清理一些资源或保存状态
    // 比如暂停定时器、保存滚动位置等
  },
  
  beforeDestroy() {
    // 移除事件监听器
    if (this.$refs.content) {
      this.$refs.content.removeEventListener('scroll', this.handleScroll);
    }
    if (this.errorHandler) {
      window.removeEventListener('error', this.errorHandler);
    }
    if (this.unhandledRejectionHandler) {
      window.removeEventListener('unhandledrejection', this.unhandledRejectionHandler);
    }
  },
  methods: {
    // 处理图片URL，添加域名前缀
    processImageUrl(imageUrl) {
      if (!imageUrl) {
        console.log('图片URL为空，使用默认图片');
        return require('@/images/img/background.png');
      }
      
      // 如果已经是完整的URL，直接返回
      if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        console.log('图片URL已是完整地址:', imageUrl);
        return imageUrl;
      }
      
      // 从环境变量获取基础URL，如果没有则使用默认值
      const baseUrl = process.env.VUE_APP_URL || 'https://jsbceshi.hfi-health.com:18188';
      console.log('使用基础URL:', baseUrl);
      
      // 确保imageUrl以/开头
      const cleanUrl = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`;
      const fullUrl = `${baseUrl}${cleanUrl}`;
      
      console.log('图片URL处理:', imageUrl, '->', fullUrl);
      return fullUrl;
    },

    // 更安全的轮播组件初始化方法
    async safeInitSwipe() {
      try {
        console.log('开始安全初始化轮播组件');

        // 首先确保容器准备就绪
        await this.waitForContainer();

        // 动态导入错误处理工具
        const { swipeErrorHandler } = await import('@/utils/swipeErrorHandler');

        // 使用错误处理工具进行安全初始化
        await swipeErrorHandler.safeInitSwipe(this, '.banner-section');

      } catch (error) {
        console.error('导入错误处理工具失败，使用备用方案:', error);
        // 备用方案：使用原有的初始化逻辑
        this.fallbackInitSwipe();
      }
    },

    // 等待容器准备就绪
    async waitForContainer() {
      return new Promise((resolve) => {
        const checkContainer = () => {
          const container = document.querySelector('.banner-section');
          if (container && container.offsetWidth > 0 && container.offsetHeight > 0) {
            console.log('轮播容器已准备就绪');
            this.containerReady = true;
            resolve();
          } else {
            console.log('等待轮播容器准备...');
            setTimeout(checkContainer, 50);
          }
        };

        this.$nextTick(() => {
          checkContainer();
        });
      });
    },

    // 备用初始化方案
    fallbackInitSwipe() {
      this.$nextTick(() => {
        const container = document.querySelector('.banner-section');
        if (!container) {
          console.log('轮播容器未找到，延迟重试');
          setTimeout(() => this.fallbackInitSwipe(), 100);
          return;
        }

        // 等待容器有实际尺寸
        const checkContainerSize = () => {
          if (container.offsetWidth > 0 && container.offsetHeight > 0) {
            console.log('轮播容器尺寸已准备就绪');
            this.containerReady = true;
            this.bannerSwipeReady = true;

            // 确保轮播组件正确初始化
            this.$nextTick(() => {
              this.ensureSwipeInitialized();
            });
          } else {
            console.log('等待轮播容器尺寸准备...');
            setTimeout(checkContainerSize, 50);
          }
        };

        checkContainerSize();
      });
    },

    // 确保轮播组件正确初始化
    ensureSwipeInitialized() {
      if (this.$refs.bannerSwipe) {
        try {
          // SafeSwipe组件的resize方法
          this.$refs.bannerSwipe.resize && this.$refs.bannerSwipe.resize();
          console.log('SafeSwipe组件已成功初始化');
        } catch (error) {
          console.log('SafeSwipe组件初始化时出现错误:', error);
          this.handleSwipeError();
        }
      } else {
        // 如果ref还没有准备好，延迟重试
        setTimeout(() => {
          this.ensureSwipeInitialized();
        }, 100);
      }
    },

    // 安全地初始化轮播组件（保留作为备用方案）
    initSwipeComponent() {
      const maxRetries = 5;
      let retryCount = 0;

      const checkAndInit = () => {
        retryCount++;

        // 检查DOM元素是否存在且有宽度
        const swipeElement = document.querySelector('.banner-swipe');
        const swipeWrapper = document.querySelector('.swipe-wrapper');

        if (swipeElement && swipeWrapper &&
            swipeElement.offsetWidth > 0 &&
            swipeWrapper.offsetWidth > 0 &&
            swipeElement.offsetHeight > 0) {
          this.bannerSwipeReady = true;
          console.log('banner轮播组件已准备就绪');

          // 确保轮播组件正确初始化
          this.$nextTick(() => {
            this.ensureSwipeInitialized();
          });
          return;
        }

        if (retryCount < maxRetries) {
          console.log(`DOM未准备好，第${retryCount}次重试...`);
          setTimeout(checkAndInit, 200 * retryCount); // 递增延迟
        } else {
          console.log('轮播组件初始化失败，使用备用方案');
          this.bannerSwipeReady = true; // 强制设置为true，避免无限等待
        }
      };

      checkAndInit();
    },
    
    // 处理轮播组件错误（简化版，主要错误处理由SafeSwipe组件负责）
    handleSwipeError(error) {
      console.log('轮播组件错误，SafeSwipe组件将自动处理', error);

      // 记录错误但不进行复杂的重试逻辑，让SafeSwipe组件处理
      this.errorRetryCount++;

      // 如果错误过多，可以考虑降级处理
      if (this.errorRetryCount > this.maxRetryCount) {
        console.log('轮播组件错误过多，考虑降级处理');
        // 可以在这里添加降级逻辑，比如显示静态图片
      }
    },
    
    async initPage() {
      // 如果数据已经加载过，跳过
      if (this.isDataLoaded) {
        console.log('数据已加载，跳过初始化');
        return;
      }
      
      // 检查是否已经有数据，如果有则跳过
      if (this.bannerList.length > 0 || this.featuredList.length > 0 || this.categoryList.length > 0 || this.activityList.length > 0) {
        console.log('检测到已有数据，跳过初始化');
        this.isDataLoaded = true;
        return;
      }
      
      console.log('初始化页面');
      try {
        // 并行加载基础数据
        const promises = [
          this.loadBannerList(),
          this.loadFeaturedList(),
          this.loadCategoryList()
        ];

        // 不等待基础数据，直接开始加载活动列表
        this.loadActivityList();

        // 等待基础数据加载完成
        await Promise.all(promises);
        console.log('基础数据加载完成');
        
        // 标记数据已加载
        this.isDataLoaded = true;

      } catch (error) {
        console.error('初始化页面失败:', error);
        // 确保活动列表能够加载
        if (this.activityList.length === 0) {
          this.loadActivityList();
        }
        // 即使出错也标记为已尝试加载
        this.isDataLoaded = true;
      }
    },
    
    bindScrollEvent() {
      if (this.$refs.content) {
        this.$refs.content.addEventListener('scroll', this.handleScroll);
      }
    },
    
    handleScroll(event) {
      const scrollTop = event.target.scrollTop;
      const bannerHeight = this.hasBannerData ? 237 : 0; // banner高度，如果没有banner则为0

      // 计算需要滚动到活动类型区域完全消失后才开始吸顶
      // 包括：banner高度 + 精选活动区域高度 + 活动类型区域高度
      const featuredSectionHeight = this.featuredList.length > 0 ? 120 : 0; // 精选活动区域高度
      const categorySectionHeight = 120; // 活动类型区域高度
      const stickyTriggerPoint = bannerHeight + featuredSectionHeight + categorySectionHeight;

      // 计算透明度：从0到1的渐变
      if (scrollTop <= bannerHeight) {
        // 在第一屏范围内，透明度为0
        this.scrollOpacity = 0;
        this.isScrolled = false;
      } else {
        // 超过第一屏，开始渐变
        const fadeDistance = 100; // 渐变距离
        const fadeStart = bannerHeight;
        const fadeEnd = bannerHeight + fadeDistance;

        if (scrollTop <= fadeEnd) {
          // 在渐变范围内
          this.scrollOpacity = Math.min((scrollTop - fadeStart) / fadeDistance, 1);
        } else {
          // 超过渐变范围，完全不透明
          this.scrollOpacity = 1;
        }

        // 只有在活动类型区域完全消失后才开始吸顶
        this.isScrolled = scrollTop >= stickyTriggerPoint;
      }
    },
    
    async loadBannerList() {
      try {
        console.log('开始加载Banner列表');
        // 调用API获取banner数据
        const response = await this.$api.getBannerList();
        console.log('Banner接口响应:', response);
        
        // 适配不同的响应数据结构
        let bannerData = [];
        if (response && response.success === 1) {
          // 旧格式：{ success: 1, value: [...] }
          bannerData = response.value || [];
        } else if (response && response.code === 200 && response.data) {
          // 新格式：{ code: 200, data: { list: [...] } }
          bannerData = response.data.list || [];
        }
        
        if (bannerData.length > 0) {
          console.log('Banner数据加载成功:', bannerData.length, '条');
          // 适配新的数据结构
          this.bannerList = bannerData.map(item => ({
            id: item.id.toString(),
            title: item.contentTitle || item.title || '',
            imageUrl: this.processImageUrl(item.contentImg),
            linkType: item.contentType === 1 ? 'activity' : 'h5',
            linkId: item.actId ? item.actId.toString() : '',
            linkUrl: item.contentType === 2 ? (item.linkUrl || item.contentTitle || '') : ''
          }));
          console.log('Banner数据适配完成:', this.bannerList);
        } else {
          console.log('Banner数据为空或加载失败');
          this.bannerList = [];
        }
      } catch (error) {
        console.error('加载Banner列表失败:', error);
        this.bannerList = [];
      }
    },
    
    async loadFeaturedList() {
      try {
        console.log('开始加载精选活动');
        // 调用API获取精选活动数据
        const response = await this.$api.getFeaturedActivityList();
        console.log('精选活动接口响应:', response);
        
        // 适配不同的响应数据结构
        let featuredData = [];
        if (response && response.success === 1) {
          // 旧格式：{ success: 1, value: [...] }
          featuredData = response.value || [];
        } else if (response && response.code === 200 && response.data) {
          // 新格式：{ code: 200, data: { list: [...] } }
          featuredData = response.data.list || [];
        }
        
        if (featuredData.length > 0) {
          // 适配新的数据结构
          this.featuredList = featuredData.map(item => ({
            id: item.id.toString(),
            title: item.contentTitle || item.title || '',
            subtitle: item.subTitle || '',
            imageUrl: this.processImageUrl(item.contentImg),
            linkType: item.contentType === 1 ? 'activity' : 'h5',
            linkId: item.actId ? item.actId.toString() : '',
            linkUrl: item.contentType === 2 ? (item.linkUrl || item.contentTitle || '') : ''
          }));
          console.log('精选活动数据适配完成:', this.featuredList);
        } else {
          this.featuredList = [];
        }
      } catch (error) {
        console.error('加载精选活动失败:', error);
        this.featuredList = [];
      }
    },
    
    async loadCategoryList() {
      try {
        console.log('开始加载活动类型');
        // 调用API获取活动类型数据
        const response = await this.$api.getActivityCategoryList();
        console.log('活动类型接口响应:', response);
        
        // 适配不同的响应数据结构
        let categoryData = [];
        if (response && response.success === 1) {
          // 旧格式：{ success: 1, value: [...] }
          categoryData = response.value || [];
        } else if (response && response.code === 200 && response.data) {
          // 新格式：{ code: 200, data: { list: [...] } }
          categoryData = response.data.list || [];
        }
        
        if (categoryData.length > 0) {
          console.log('活动类型原始数据:', categoryData);
          // 适配新的数据结构
          this.categoryList = categoryData.map(item => {
            console.log('处理活动类型项:', item);
            const processedItem = {
              id: item.typeId.toString(),
              name: item.typeTitle || '',
              icon: this.processImageUrl(item.typeCoverImg || ''), // 使用正确的字段名 typeCoverImg
              sort: item.sort || 0
            };
            console.log('处理后的活动类型项:', processedItem);
            return processedItem;
          });
        } else {
          this.categoryList = [];
        }
      } catch (error) {
        console.error('加载活动类型失败:', error);
        this.categoryList = [];
      }
    },
    
    async loadActivityList() {
      // 防止重复加载，但允许下拉刷新时重新加载
      if (this.loading && !this.refreshing) {
        console.log('loadActivityList被阻止：loading=true, refreshing=false');
        return;
      }

      console.log('开始加载活动列表，pageNum:', this.pageNum, 'loading:', this.loading, 'refreshing:', this.refreshing);
      this.loading = true;
      try {
        let response;

        // 判断是否使用筛选接口 - 使用计算属性确保逻辑一致
        const hasFilter = this.hasActiveFilter;
        console.log('是否使用筛选接口:', hasFilter, '筛选条件:', this.filterForm);

        // 准备排序参数
        const sortParams = {
          popularity: this.sortType === 'popularity' ? 1 : 0, // 1选择人气排行，0不选
          selectSort: this.popularityOrder === 'asc' ? 1 : 0 // 1升序，0降序
        };
        console.log('排序参数:', sortParams);

        if (hasFilter) {
            // 1.7 首页筛选活动接口
            const params = {
              typeId: this.activeCategory === 'all' ? null : (this.activeCategory ? parseInt(this.activeCategory) : null),
              activityStatus: this.filterForm.activityStatus === 'all' ? null : this.filterForm.activityStatus,
              registerStatus: this.filterForm.registrationStatus === 'all' ? null : this.filterForm.registrationStatus,
              pageNum: this.pageNum,
              pageSize: this.pageSize,
              ...sortParams // 传递排序参数
            };
            console.log('调用chooseActivity接口，参数:', params);
            response = await this.$api.chooseActivity(params);
          } else {
            // 1.6 首页的活动列表接口
            const params = {
              typeId: this.activeCategory === 'all' ? null : (this.activeCategory ? parseInt(this.activeCategory) : null),
              pageNum: this.pageNum,
              pageSize: this.pageSize,
              ...sortParams // 传递排序参数
            };
            console.log('调用getActivityList接口，参数:', params);
            response = await this.$api.getActivityList(params);
          }
          console.log('API响应:', response);

        // 适配不同的响应数据结构
        let activities = [];
        if (response && response.success === 1) {
          // 旧格式：{ success: 1, value: [...] }
          activities = response.value || [];
        } else if (response && response.code === 200 && response.data) {
          // 新格式：{ code: 200, data: { list: [...] } }
          activities = response.data.list || [];
        }
        
        if (activities.length > 0) {
          console.log('获取到的活动数据:', activities.length, '条');

          // 转换数据格式以适配页面显示
          const formattedActivities = activities.map(item => ({
            id: item.id,
            title: item.actTitle,
            imageUrl: this.processImageUrl(item.headerImg),
            headerImgId: item.headerImgId,
            activityStatus: item.activityStatus,
            statusText: this.getActivityStatusText(item.activityStatus),
            statusClass: this.getStatusClass(item.activityStatus),
            activityTime: item.actTime,
            registrationTime: item.registerTime,
            timeText: item.actTime,
            maxParticipants: item.numRage,
            participantCount: item.registrantsNum,
            userRegistered: item.register === 1, // 根据新接口的register字段判断是否已报名
            registrationStatus: 'ongoing' // 默认为进行中
          }));
          console.log('格式化后的活动数据:', formattedActivities.length, '条');

          // 处理数据显示
          if (this.pageNum === 1) {
            this.activityList = formattedActivities;
          } else {
            this.activityList.push(...formattedActivities);
          }

          // 设置加载结束状态（优先依据总数，否则按页大小判断）
          let total = 0;
          if (response && response.success === 1 && typeof response.total === 'number') {
            // 旧格式
            total = response.total;
          } else if (response && response.code === 200 && response.data && typeof response.data.total === 'number') {
            // 新格式
            total = response.data.total;
          }
          
          if (total > 0) {
            this.total = total;
            this.finished = this.pageNum * this.pageSize >= this.total;
          } else {
            // 没有total字段，根据返回数据量判断是否还有更多数据
            this.finished = formattedActivities.length < this.pageSize;
          }

          // 递增页码
          this.pageNum++;
        } else {
          // 接口返回失败或没有数据的情况
          console.log('接口返回失败或无数据，response:', response);
          this.finished = true;
          if (this.pageNum === 1) {
            this.activityList = [];
          }
        }
      } catch (error) {
        console.error('获取活动列表失败:', error);
        this.finished = true;
        if (this.pageNum === 1) {
          this.activityList = [];
        }
      } finally {
        console.log('加载完成，loading设置为false，refreshing状态:', this.refreshing);
        this.loading = false;
      }
    },
    
    goToSearch() {
      this.$router.push('/search');
    },
    
    getStickyHeaderStyle() {
      // 如果没有banner数据，直接显示渐变背景
      if (!this.hasBannerData) {
        return {
          background: 'linear-gradient(to bottom, #F5EBDE, #F3E5D2)'
        };
      }

      // 如果有banner数据，根据滚动位置动态设置透明度
      if (this.scrollOpacity > 0) {
        return {
          background: `linear-gradient(to bottom,
            rgba(245, 235, 222, ${this.scrollOpacity}),
            rgba(243, 229, 210, ${this.scrollOpacity}))`
        };
      }

      // 默认透明
      return {
        background: 'transparent'
      };
    },
    
    // Banner 相关事件处理
    onBannerChange(index) {
      console.log('Banner切换到:', index);
    },

    onSwipeReady() {
      console.log('SafeSwipe组件已准备就绪');
      this.bannerSwipeReady = true;
      this.containerReady = true;
      // 重置错误计数
      this.errorRetryCount = 0;
    },

    onBannerImageLoad() {
      this.bannerImagesLoaded++;
      console.log(`Banner图片加载完成: ${this.bannerImagesLoaded}/${this.bannerList.length}`);

      // 如果所有图片都加载完成，且轮播组件还没有准备好，尝试重新初始化
      if (this.bannerImagesLoaded >= this.bannerList.length &&
          this.bannerList.length > 1 &&
          !this.bannerSwipeReady) {
        console.log('所有Banner图片加载完成，尝试初始化轮播组件');
        this.$nextTick(() => {
          this.safeInitSwipe();
        });
      }
    },

    onBannerImageError(event) {
      console.error('Banner图片加载失败:', event.target.src);
      // 即使图片加载失败，也要计入已加载数量，避免无限等待
      this.bannerImagesLoaded++;

      // 如果所有图片都处理完成（包括失败的），尝试初始化轮播组件
      if (this.bannerImagesLoaded >= this.bannerList.length &&
          this.bannerList.length > 1 &&
          !this.bannerSwipeReady) {
        console.log('Banner图片处理完成（包含失败），尝试初始化轮播组件');
        this.$nextTick(() => {
          this.safeInitSwipe();
        });
      }
    },

    handleBannerClick(banner) {
      try {
        console.log('Banner点击:', banner);
        
        if (banner.linkType === 'activity' && banner.linkId) {
          // 跳转到活动详情页面
          console.log('跳转到活动详情页面:', banner.linkId);
          this.goToActivityDetail({ id: banner.linkId });
        } else if (banner.linkType === 'h5' && banner.linkUrl) {
          // 打开H5链接
          console.log('打开H5链接:', banner.linkUrl);
          window.open(banner.linkUrl, '_blank');
        } else {
          console.log('Banner链接信息不完整:', banner);
        }
      } catch (error) {
        console.error('Banner点击处理失败:', error);
      }
    },
    
    handleFeaturedClick(item) {
      try {
        console.log('精选活动点击:', item);
        
        if (item.linkType === 'activity' && item.linkId) {
          // 跳转到活动详情页面
          console.log('跳转到活动详情页面:', item.linkId);
          this.goToActivityDetail({ id: item.linkId });
        } else if (item.linkType === 'h5' && item.linkUrl) {
          // 打开H5链接
          console.log('打开H5链接:', item.linkUrl);
          window.open(item.linkUrl, '_blank');
        } else {
          console.log('精选活动链接信息不完整:', item);
        }
      } catch (error) {
        console.error('精选活动点击处理失败:', error);
      }
    },
    
    handleCategoryChange(name) {
      console.log('切换分类:', name);
      this.activeCategory = name;
      this.resetList();
    },
    
    handleSort(type) {
      if (type === 'popularity' && this.sortType === 'popularity') {
        // 切换升序/降序
        this.popularityOrder = this.popularityOrder === 'asc' ? 'desc' : 'asc';
      } else {
        this.sortType = type;
        if (type === 'popularity') {
          this.popularityOrder = 'desc';
        }
      }
      
      this.resetList();
      
      // 在数据加载完成后，恢复到吸顶位置
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.content) {
            // 计算吸顶触发点的位置
            const bannerHeight = this.hasBannerData ? 237 : 0;
            const featuredSectionHeight = this.featuredList.length > 0 ? 120 : 0;
            const categorySectionHeight = 120; // 活动类型区域高度
            const stickyTriggerPoint = bannerHeight + featuredSectionHeight + categorySectionHeight;
            
            // 滚动到吸顶位置
            this.$refs.content.scrollTop = stickyTriggerPoint;
          }
        }, 100);
      });
    },
    
    resetFilter() {
      this.filterForm = {
        categoryId: 'all',
        activityStatus: 'all',
        registrationStatus: 'all'
      };
    },

    // 切换筛选弹窗显示/隐藏
    toggleFilter() {
      this.showFilterPopup = !this.showFilterPopup;
    },

    applyFilter() {
      this.showFilterPopup = false;
      // 如果筛选了活动类型，需要同步到tab
      if (this.filterForm.categoryId !== 'all') {
        this.activeCategory = this.filterForm.categoryId;
      }
      this.resetList();
    },
    
    resetList() {
      console.log('重置列表状态，当前状态 - loading:', this.loading, 'refreshing:', this.refreshing);
      this.pageNum = 1;
      this.total = 0;
      this.finished = false;
      this.loading = false; // 重置loading状态
      this.activityList = [];
      
      // 确保状态重置后再调用加载方法
      this.$nextTick(() => {
        this.loadActivityList();
      });
    },
 
    onRefresh() {
      console.log('开始下拉刷新，当前状态 - loading:', this.loading, 'refreshing:', this.refreshing);
      this.refreshing = true;
      this.pageNum = 1;
      this.total = 0;
      this.finished = false;
      this.loading = false; // 重置loading状态，确保可以重新加载
      this.activityList = [];
      
      // 重置数据加载状态，允许重新加载
      this.isDataLoaded = false;
      
      // 确保状态重置后再调用加载方法
      this.$nextTick(() => {
        this.loadActivityList().finally(() => {
          console.log('下拉刷新完成');
          this.refreshing = false;
        });
      });
    },
    
    goToActivityDetail(activity) {
      this.$router.push(`/activity/${activity.id}`);
    },

    getActivityStatusText(status) {
      // 直接返回API返回的中文状态值
      return status || '未知状态';
    },

    getStatusClass(status) {
      const statusMap = {
        '未开始': 'status-not-started',
        '进行中': 'status-ongoing',
        '已结束': 'status-ended'
      };
      return statusMap[status] || 'status-not-started';
    },

    getMaxParticipantsText(maxParticipants) {
      return maxParticipants === 'unlimited' ? '不限' : maxParticipants;
    },

    shouldShowButton(activity) {
      // 根据活动状态判断是否显示按钮
      // 只有未开始状态才显示一键报名按钮
      return activity.activityStatus === '未开始';
    },

    getButtonClass(activity) {
      if (activity.userRegistered) {
        return 'registered';
      }
      return 'primary';
    },

    getButtonText(activity) {
      if (activity.userRegistered) {
        return '已报名';
      }
      return '一键报名';
    },

    handleActivityAction(activity) {
      if (activity.userRegistered) {
        // 已报名，跳转到报名详情页
        this.$router.push(`/registration-detail/${activity.id}`);
      } else {
        // 未报名，跳转到活动详情页进行报名
        this.goToActivityDetail(activity);
      }
    },



    // // 调试方法
    // debugLoadData() {
    //   console.log('=== 调试信息 ===');
    //   console.log('当前状态:', {
    //     loading: this.loading,
    //     finished: this.finished,
    //     pageNum: this.pageNum,
    //     activityListLength: this.activityList.length,
    //     sortType: this.sortType,
    //     activeCategory: this.activeCategory,
    //     hasActiveFilter: this.hasActiveFilter
    //   });

    //   // 重置状态并重新加载
    //   this.pageNum = 1;
    //   this.finished = false;
    //   this.activityList = [];
    //   this.loading = false;

    //   console.log('开始重新加载数据...');
    //   this.loadActivityList();
    // }
  }
};
</script>

<style lang="less" scoped>
.home-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  background: white;
}

// 统一吸顶容器
.sticky-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: transparent;
  transition: all 0.3s ease;
  pointer-events: auto;

  // 没有banner数据时显示渐变背景
  &.sticky-header-no-banner {
    background: linear-gradient(to bottom, #F5EBDE, #F3E5D2);
  }

  // 滚动时的动态背景 - 作为一个整体
  &.sticky-header-scrolled {
    background: linear-gradient(to bottom, #F5EBDE, #F3E5D2);
    backdrop-filter: blur(10px);
  }

  // 搜索框样式
  .search-input {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 8px 16px 4px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 24px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .van-icon {
      margin-right: 8px;
      color: #666;
      transition: color 0.3s ease;
    }

    .placeholder {
      color: #666;
      font-size: 14px;
      transition: color 0.3s ease;
    }
  }

  // 活动类型区域样式
  .category-container {
    background: linear-gradient(180deg, #F3E5D2 0%, #FFFFFF 100%);
    padding: 8px 16px 6px 5px;

    .category-scroll {
      display: flex;
      gap: 0;

      &:not(.can-scroll) {
        .category-item {
          flex: 1;
        }
      }

      &.can-scroll {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .category-item {
          min-width: 80px;
          flex-shrink: 0;
        }
      }

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 4px;
        cursor: pointer;
        transition: all 0.3s ease;

        .category-icon-wrapper {
          margin-bottom: 6px;

          .category-icon {
            width: 34px;
            height: 34px;
            border-radius: 12px; // 增加圆角角度，更美观
            border: none; // 移除虚线边框，使用无痕边框
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            overflow: hidden; // 确保图片不会超出圆角边界

            img {
              width: 100%;
              height: 100%;
              object-fit: cover; // 完全填充容器，保持比例
              border-radius: 7px; // 稍微小一点，避免与边框重叠
            }

            // 全部活动的特殊图标
            &.all-icon {
              background: transparent !important; // 确保容器背景透明
              
              .all-type-image {
                width: 100%;
                height: 100%;
                object-fit: contain; // 改为contain，保持图片完整显示
                border-radius: 0; // 去除圆角，避免背景溢出
                background: transparent; // 确保图片背景透明
              }
            }
          }
        }

        // 兼容旧版本的直接 .category-icon 样式
        .category-icon {
          width: 34px;
          height: 34px;
          border-radius: 16px; // 进一步增加圆角角度，更美观
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 6px;
          border: none; // 移除虚线边框，使用无痕边框
          background: rgba(212, 165, 116, 0.1);
          transition: all 0.3s ease;
          overflow: hidden; // 确保图片不会超出圆角边界

          img {
            width: 100%;
            height: 100%;
            object-fit: cover; // 完全填充容器，保持比例
            border-radius: 10px; // 稍微小一点，避免与边框重叠
          }

          .default-icon {
            font-size: 18px;
            font-weight: bold;
            color: #D4A574;
          }
        }

        .category-name {
          font-size: 12px;
          color: #666;
          text-align: center;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: color 0.3s ease;
        }

        &.active {
          .category-icon-wrapper .category-icon {
            border: none; // 激活状态也使用无痕边框
            background: linear-gradient(to right, #C08A48, #E2B279);
            box-shadow: 0 2px 8px rgba(212, 165, 116, 0.2);

            &.all-icon {
              background: transparent !important; // 激活状态下也保持透明背景
              
              .all-type-image {
                opacity: 1; // 激活状态下图片保持原样
              }
            }
          }

          .category-icon {
            border: none; // 激活状态也使用无痕边框
            background: linear-gradient(to right, #C08A48, #E2B279);

            .default-icon {
              color: white;
            }
          }

          .category-name {
            color: #D4A574;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
  }

  // 筛选区域样式
  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 16px 8px;
    background: white;

    // 左边按钮样式
    .sort-btn {
      background: #FFFAF7; // 未选中状态背景色
      border: 0.25px solid #E7C59A; // 未选中状态边框
      border-radius: 4px; // 未选中状态圆角
      color: #666;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
      font-size: 11px; // 字号11pt
      font-weight: 400; // 字重Regular
      line-height: 15px; // 行高15pt
      letter-spacing: 0; // 字间距0pt
      padding: 6px 12px; // 减少内边距，让按钮更紧凑
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 2px; // 减少图标和文字的间距
      white-space: nowrap; // 防止文字换行
      min-width: fit-content; // 确保按钮宽度适应内容

      &.active {
        background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 选中状态渐变背景
        border-color: transparent; // 选中状态无边框
        color: white; // 选中状态文字颜色
      }

      &:hover {
        border-color: #D4A574;
      }
    }

    .right-buttons {
      display: flex;
      gap: 20px; // 右边两个按钮之间的间距

      .sort-btn {
        background: #FFFAF7; // 未选中状态背景色
        border: 0.25px solid #E7C59A; // 未选中状态边框
        border-radius: 4px; // 未选中状态圆角
        color: #666;
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
        font-size: 11px; // 字号11pt
        font-weight: 400; // 字重Regular
        line-height: 15px; // 行高15pt
        letter-spacing: 0; // 字间距0pt
        padding: 6px 12px; // 减少内边距，让按钮更紧凑
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 2px; // 减少图标和文字的间距
        text-align: left; // 左对齐
        white-space: nowrap; // 防止文字换行
        min-width: fit-content; // 确保按钮宽度适应内容

        &.active {
          background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 使用筛选弹窗的渐变样式
          border-color: transparent;
          color: white;
          font-weight: 400; // 保持Regular字重
          padding: 6px 12px; // 保持与未选中状态相同的padding
          justify-content: flex-start; // 左对齐
        }

        &:hover:not(.active) {
          background: #F5F5F5;
          border-color: #D4A574;
        }
      }
    }

    .filter-btn {
      background: #FFFAF7; // 未选中状态背景色
      border: 0.25px solid #E7C59A; // 未选中状态边框
      border-radius: 4px; // 未选中状态圆角
      color: #666;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
      font-size: 11px; // 字号11pt
      font-weight: 400; // 字重Regular
      line-height: 15px; // 行高15pt
      letter-spacing: 0; // 字间距0pt
      padding: 6px 12px; // 与其他按钮保持一致
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 2px; // 图标和文字间距
      text-align: left; // 左对齐
      white-space: nowrap; // 防止文字换行
      min-width: fit-content; // 确保按钮宽度适应内容

      &.active {
        background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 使用筛选弹窗的渐变样式
        border-color: transparent;
        color: white;
      }

      &:hover:not(.active) {
        background: #F5F5F5;
        border-color: #D4A574;
      }
    }
  }
}

.content {
  flex: 1;
  overflow-y: auto;
  padding-top: 0; // 移除顶部padding，让banner图片从顶部开始
  background: white;
}

.banner-section {
  position: relative;
  width: 100%;
  min-height: 237px; // 确保最小高度

  .swipe-wrapper {
    position: relative;
    overflow: hidden;
    width: 100%;
    height: 237px; // 固定高度
    // 确保容器有明确的尺寸
    min-width: 100%;
    min-height: 237px;
  }

  .banner-swipe {
    height: 237px !important; // 强制设置高度
    width: 100% !important; // 使用100%宽度而不是固定宽度
    margin: 0 auto; // 居中显示
    // 确保轮播组件有正确的盒模型
    box-sizing: border-box;

    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block; // 确保图片正确显示
    }
  }

  .banner-single {
    height: 237px; // 设置为237pt
    width: 100%; // 使用100%宽度
    margin: 0 auto; // 居中显示

    .banner-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }
  }

  .banner-placeholder {
    height: 237px;
    width: 100%; // 使用100%宽度
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;

    .banner-loading {
      color: #999;
      font-size: 14px;
    }
  }
}

.featured-section {
  width: 100%; // 占满整行
  margin: 0;
  padding: 0;
  
  h3.section-title {
    position: relative;
    width: 100%;
    height: 35.5px; // 严格控制高度为35.5pt
    margin: 0;
    padding: 0;
    background: linear-gradient(to right, #C08A48 0%, #EEC18B 100%);
    display: flex;
    align-items: center;
    justify-content: flex-start;
    box-sizing: border-box;
    
    // 文字样式
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 17px; // 字号17pt
    font-weight: 580; // 字重Semibold (600)
    color: #FFFFFF !important; // 文字颜色#FFFFFF 100%，使用!important确保优先级
    text-align: left; // 左对齐
    line-height: 24px; // 行高24pt
    letter-spacing: 0; // 字间距0pt
    
    // 防止换行
    white-space: nowrap; // 强制不换行
    overflow: hidden; // 隐藏溢出内容
    text-overflow: ellipsis; // 超出部分显示省略号
    
    // 使用padding来定位文字
    padding-left: 23px; // 距离最左侧23pt
    padding-right: 284px; // 距离最右侧284pt
    padding-top: 6px; // 距离最上侧6pt
    padding-bottom: 5.5px; // 距离最下侧5.5pt
  }
  
  .featured-grid {
    display: grid;
    grid-template-columns: 1fr 1fr; // 2列网格
    gap: 9px; // 进一步减小网格间距
    padding: 14px; // 进一步减小内边距
    background: #f8f9fa; // 统一的柔和背景色
    box-sizing: border-box; // 确保padding计算在内
    width: 100%; // 确保宽度不超出
    max-width: 100%; // 限制最大宽度
    
    .featured-item {
      position: relative;
      background: #ffffff; // 基础白色背景
      border-radius: 4px; // 4pt圆角
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: flex-start; // 改为顶部对齐，让图标和文字都从顶部开始
      padding-left: 12px; // 进一步减少内边距
      padding-right: 12px;
      padding-top: 12px; // 添加顶部内边距，给内容一些空间
      padding-bottom: 12px; // 添加底部内边距，给内容一些空间
      min-height: 70px; // 减小最小高度
      box-sizing: border-box; // 确保padding计算在内
      width: 171px; // 确保宽度不超出
      height: 65px; // 确保高度不超出
      max-width: 100%; // 限制最大宽度
      overflow: hidden; // 防止内容溢出
      
      // 根据设计稿的复杂渐变背景
      background: 
        // 第三层：暖色系三色渐变 (对角线)
        linear-gradient(135deg, 
          rgba(244, 232, 217, 1) 0%, 
          rgba(237, 206, 165, 1) 70%, 
          rgba(255, 254, 253, 1) 100%),
        // 第二层：蓝灰色渐变 (水平)
        linear-gradient(to left, 
          rgba(230, 238, 245, 1) 0%, 
          rgba(255, 255, 255, 1) 100%),
        // 第一层：蓝色系渐变 (水平)
        linear-gradient(to left, 
          rgba(231, 243, 255, 1) 0%, 
          rgba(255, 255, 255, 1) 100%);
      
      // 白色内阴影效果 (根据设计稿)
      box-shadow: 
        inset -0.5px 0.5px 0px rgba(255, 255, 255, 1),
        0 2px 8px rgba(0, 0, 0, 0.05);
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 
          inset -0.5px 0.5px 0px rgba(255, 255, 255, 1),
          0 4px 16px rgba(0, 0, 0, 0.1);
        // 保持渐变背景，稍微增强效果
        background: 
          linear-gradient(135deg, 
            rgba(244, 232, 217, 1) 0%, 
            rgba(237, 206, 165, 1) 70%, 
            rgba(255, 254, 253, 1) 100%),
          linear-gradient(to left, 
            rgba(230, 238, 245, 1) 0%, 
            rgba(255, 255, 255, 1) 100%),
          linear-gradient(to left, 
            rgba(231, 243, 255, 1) 0%, 
            rgba(255, 255, 255, 1) 100%);
      }
      
      .featured-icon {
        flex-shrink: 0;
        width: 44px; // 图标宽度44pt
        height: 44px; // 图标高度44pt
        margin-right: 8px; // 保持间距
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 16px; // 小圆弧效果
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 3; // 确保图标在渐变层之上
        
        .featured-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 16px; // 相应减小圆角
        }
      }
      
      .featured-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; // 改为顶部对齐
        align-items: flex-start; // 左对齐
        min-width: 0; // 允许内容收缩
        overflow: hidden; // 防止内容溢出
        padding-top: 0; // 移除顶部内边距，让标题与图标顶部对齐
        position: relative;
        z-index: 3; // 确保内容在渐变层之上
        
        .featured-title {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 14px; // 字号14pt
          font-weight: 500; // 字重Medium (中黑体)
          margin-bottom: 2px; // 保持间距
          color: #2F2F2F; // 颜色#2F2F2F 100%
          line-height: 20px; // 行高20pt
          letter-spacing: 0; // 字间距0pt
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left; // 左对齐
          padding-top: 0; // 确保顶部无内边距
          margin-top: 0; // 确保顶部无外边距
        }
        
        .featured-subtitle {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-size: 9px; // 字号9pt
          font-weight: 400; // 字重Regular (常规体)
          color: #966233; // 颜色#966233 100%
          line-height: 12.5px; // 行高12.5pt
          letter-spacing: 0; // 字间距0pt
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          text-align: left; // 左对齐
        }
      }
    }
    
    // 当只有1个时，只显示左上角
    &:has(.featured-item:nth-child(1):last-child) {
      grid-template-columns: 1fr;
      grid-template-rows: auto;
      justify-items: start;
      
      .featured-item {
        max-width: calc(50% - 6px);
      }
    }
    
    // 当只有2个时，只展示一行
    &:has(.featured-item:nth-child(2):last-child) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto;
    }
    
    // 当只有3个时，显示左上、右上、左下，右下空缺
    &:has(.featured-item:nth-child(3):last-child) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
      
      .featured-item:nth-child(3) {
        grid-column: 1;
        grid-row: 2;
      }
    }
    
    // 当有4个时，完整的2x2网格
    &:has(.featured-item:nth-child(4):last-child) {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: auto auto;
    }
  }
}

// 活动类型区域
.category-section {
  margin: 0;
  transition: all 0.3s ease;

  .category-container {
    // 按照设计图的渐变背景：从 #F3E5D2 到 #FFFFFF
    background: linear-gradient(180deg, #F3E5D2 0%, #FFFFFF 100%);
    border-radius: 16.5px 16.5px 0 0; // 只有上面有圆角，下面没有圆角
    padding: 20px 16px 20px 5px;
    transition: all 0.3s ease;

    .category-scroll {
      display: flex;
      gap: 0; // 无间距，均分

      // 如果类型数量不超过4个，均分显示
      &:not(.can-scroll) {
        .category-item {
          flex: 1; // 均分宽度
        }
      }

      // 如果类型数量超过4个，支持滑动
      &.can-scroll {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        .category-item {
          flex: 0 0 auto;
          min-width: 80px; // 最小宽度
        }
      }

      .category-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left:0;

        .category-icon-wrapper {
          margin-bottom: 8px;

          .category-icon {
            width: 34px;
            height: 34px;
            border-radius: 12px; // 增加圆角角度，更美观
            border: none; // 移除虚线边框，使用无痕边框
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.8);
            transition: all 0.3s ease;
            overflow: hidden; // 确保图片不会超出圆角边界

            img {
              width: 100%;
              height: 100%;
              object-fit: cover; // 完全填充容器，保持比例
              border-radius: 7px; // 稍微小一点，避免与边框重叠
            }

            // 全部活动的特殊图标
            &.all-icon {
              background: transparent !important; // 确保容器背景透明
              
              .all-type-image {
                width: 100%;
                height: 100%;
                object-fit: contain; // 改为contain，保持图片完整显示
                border-radius: 0; // 去除圆角，避免背景溢出
                background: transparent; // 确保图片背景透明
              }
            }
          }
        }

        .category-name {
          font-size: 12px;
          color: #333;
          text-align: center;
          line-height: 1.2;
          font-weight: 400;
        }

        // 激活状态
        &.active {
          .category-icon {
            border: none; // 激活状态也使用无痕边框
            background: linear-gradient(to right, #C08A48, #E2B279);
            box-shadow: 0 2px 8px rgba(212, 165, 116, 0.2);
          }

          .category-name {
            color: #D4A574;
            font-weight: 600;
            font-size: 16px;
          }
        }
      }
    }
  }


}



// 筛选区域
.filter-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  transition: all 0.3s ease;

  // 左边按钮样式
  .sort-btn {
    background: #FFFAF7; // 未选中状态背景色
    border: 0.25px solid #E7C59A; // 未选中状态边框
    border-radius: 4px; // 未选中状态圆角
    color: #666;
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
    font-size: 11px; // 字号11pt
    font-weight: 400; // 字重Regular
    line-height: 15px; // 行高15pt
    letter-spacing: 0; // 字间距0pt
    padding: 6px 12px; // 减少内边距，让按钮更紧凑
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px; // 减少图标和文字的间距
    white-space: nowrap; // 防止文字换行
    min-width: fit-content; // 确保按钮宽度适应内容

    &.active {
      background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 选中状态渐变背景
      border-color: transparent; // 选中状态无边框
      color: white; // 选中状态文字颜色
    }

    &:hover {
      border-color: #D4A574;
    }
  }

  .right-buttons {
    display: flex;
    gap: 20px; // 右边两个按钮之间的间距

    .sort-btn {
      background: #FFFAF7; // 未选中状态背景色
      border: 0.25px solid #E7C59A; // 未选中状态边框
      border-radius: 4px; // 未选中状态圆角
      color: #666;
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
      font-size: 11px; // 字号11pt
      font-weight: 400; // 字重Regular
      line-height: 15px; // 行高15pt
      letter-spacing: 0; // 字间距0pt
      padding: 6px 12px; // 减少内边距，让按钮更紧凑
      display: flex;
      align-items: center;
      gap: 2px; // 减少图标和文字的间距
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left; // 左对齐
      white-space: nowrap; // 防止文字换行
      min-width: fit-content; // 确保按钮宽度适应内容

      &.active {
        background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 使用筛选弹窗的渐变样式
        border-color: transparent;
        color: white;
        font-weight: 400; // 保持Regular字重
        padding: 6px 12px; // 保持与未选中状态相同的padding
        justify-content: flex-start; // 左对齐
      }

      &:hover {
        background: #F5F5F5;
        border-color: #D4A574;

        &.active {
          background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%);
        }
      }
    }
  }

  .filter-btn {
    background: #FFFAF7; // 未选中状态背景色
    border: 0.25px solid #E7C59A; // 未选中状态边框
    border-radius: 4px; // 未选中状态圆角
    color: #666;
    font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
    font-size: 11px; // 字号11pt
    font-weight: 400; // 字重Regular
    line-height: 15px; // 行高15pt
    letter-spacing: 0; // 字间距0pt
    padding: 6px 12px; // 与其他按钮保持一致
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 2px; // 图标和文字间距
    text-align: left; // 左对齐
    white-space: nowrap; // 防止文字换行
    min-width: fit-content; // 确保按钮宽度适应内容

    &.active {
      background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 使用筛选弹窗的渐变样式
      border-color: transparent;
      color: white;
    }

    &:hover {
      background: #F5F5F5;
      border-color: #D4A574;

      &.active {
        background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%);
      }
    }
  }


}

// 筛选区域样式（在sticky-section内部已定义）

.activity-list {
  padding: 16px 12px 80px 12px; // 增加底部间距，避免被底部导航栏遮挡
  // 确保活动列表的层级低于吸顶头部
  position: relative;
  z-index: 1;

  // 自定义 van-list 组件的加载样式
  :deep(.van-list__loading) {
    padding: 20px 0;
    text-align: center;
    color: #999;
    font-size: 14px;
    
    .van-loading {
      margin-right: 8px;
    }
  }

  // 自定义 van-list 组件的完成提示样式
  :deep(.van-list__finished-text) {
    padding: 20px 0 80px 0; // 增加底部间距，避免被底部导航栏遮挡
    text-align: center;
    color: #999;
    font-size: 14px;
    font-style: italic;
  }

  .activity-item {
    border: 0.3px solid #f9ecd9;
    background: white;
    border-radius: 12px 12px 12px 12px;
    margin-bottom: 16px;
    overflow: visible; // 改为visible，让贴纸可以正常显示
    // 设置相对定位和层级
    position: relative;
    z-index: 1;

    .activity-image-wrapper {
      position: relative;
      width: 100%;
      height: 160px;
      overflow: visible; // 允许贴纸显示
      border-radius: 12px 12px 0 0;

      .activity-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
        border-radius: 12px 12px 0 0;
      }

      .activity-code-bg {
        position: absolute;
        top: 0;
        right: 0;
        width: 130px; // 稍微减小尺寸，避免超出太多
        height: auto;
        object-fit: contain;
        z-index: 1;
        // 确保贴纸尺寸合适
        max-width: 130px;
        max-height: 50px;
      }

      .activity-code {
        position: absolute;
        top: 0; // 再往上
        right: 8px; // 再往右
        color: white;
        font-size: 10px;
        font-family: 'PingFang SC', '苹方-简', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-weight: 400; // Regular
        text-align: left;
        z-index: 2;

        // 文字定位在贴纸上
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px; // 适配贴纸的文字区域
        height: 26px;

        // 文字样式
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        white-space: nowrap;
        text-align: center;
        line-height: 1;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5); // 增强文字阴影
        pointer-events: none; // 防止文字阻挡点击事件
      }
    }

    .activity-content-wrapper {
      background: linear-gradient(180deg, #F5EBDF 0%, #FFFFFF 100%);
      padding: 16px;

      .activity-title {
        font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 16px;
        font-weight: 500; // Medium
        line-height: 22.5px;
        color: #373737;
        margin: 0 0 16px 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
      }
    }

    .activity-info-section {
      padding: 0;
      margin-bottom: 16px;

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 11px;

        .info-label {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 400; // Regular
          line-height: 15px;
          color: #373737;
          min-width: 60px; // 减少宽度，让标签离内容更近
          margin-right: 4px; // 添加右边距
        }

        .info-value {
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          font-weight: 400; // Regular
          line-height: 15px;
          color: #373737;
          flex: 1;
        }

        // 报名时间和活动时间的特殊颜色
        &:nth-child(2), &:nth-child(3) {
          .info-value {
            color: #cb9d65; // 金色
          }
        }

        // 报名人数的特殊颜色
        &:nth-child(4) {
          .info-value {
            color: #a2a2a2; // 灰色
          }
        }

        .activity-status {
          padding: 2px 8px;
          border-radius: 13.5px;
          font-size: 11px;
          font-weight: 500;
          color: white;

          &.status-not-started {
            background: linear-gradient(135deg, #C08A48 0%, #EDC391 100%);
            color: white;
          }

          &.status-ongoing {
            background: linear-gradient(135deg, #00CA3B 0%, #05B06D 100%);
            color: white;
          }

          &.status-ended {
            background: #CCCCCC;
            color: white;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .activity-actions {
      padding: 0;

      .action-button {
        width: 100%;
        height: 42px;
        border-radius: 21px;
        border: none;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;

        &.primary {
          background: linear-gradient(135deg, #C08A48 0%, #EDC391 100%);
          color: white;
          
          &:hover {
            background: linear-gradient(135deg, #B07A38 0%, #DDC381 100%);
          }
        }

        &.registered {
          background: #CCCCCC;
          color: white;
          
          &:hover {
            background: #BBBBBB;
          }
        }
      }
    }
  }
}

.filter-popup {
  padding: 0;
  height: 50vh;
  display: flex;
  flex-direction: column;
  background: white; // 背景改为白色
  border-radius: 0;

  .filter-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: white; // 确保内容区域背景为白色

    .filter-group {
      margin-bottom: 24px;

      .filter-title {
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.2;
      }

      .filter-options {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;

        .filter-option-btn {
          width: 100px; // 宽度100pt
          height: 30.5px; // 高度30.5pt
          border: 0.56px solid #D6BCA4; // 边框粗细0.56pt，颜色#D6BCA4
          border-radius: 15.5px; // 圆角15.5pt
          background: white;
          color: #C58D5B; // 文字颜色#C58D5B
          font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; // 苹方-简
          font-size: 12px; // 字号12pt
          font-weight: 400; // 字重Regular
          line-height: 16.5px; // 行高16.5pt
          letter-spacing: 0; // 字间距0pt
          text-align: center; // 居中对齐
          cursor: pointer;
          transition: all 0.3s ease;
          outline: none;
          display: flex;
          align-items: center;
          justify-content: center;
          box-sizing: border-box;

          &:hover {
            border-color: #C58D5B;
            color: #C58D5B;
          }

          &.active {
            background: linear-gradient(135deg, #9D8262 0%, #432A0C 100%); // 选中渐变：从#9D8262到#432A0C
            border-color: transparent;
            color: white;
            font-weight: 400;
          }
        }
      }
    }
  }

  .filter-footer {
    display: flex;
    gap: 12px;
    padding: 20px;
    background: white;
    border-top: 1px solid #f0f0f0;

    .filter-reset-btn {
      flex: 1;
      height: 44px;
      border: 1px solid #E0E0E0;
      border-radius: 22px;
      background: white;
      color: #666;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      outline: none;

      &:hover {
        background: #F5F5F5;
        border-color: #D4A574;
        color: #D4A574;
      }
    }

    .filter-confirm-btn {
      flex: 1;
      height: 44px;
      border: none;
      border-radius: 22px;
      background: linear-gradient(135deg, #D4A574, #C19660);
      color: white;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      outline: none;
      box-shadow: 0 2px 8px rgba(212, 165, 116, 0.3);

      &:hover {
        background: linear-gradient(135deg, #C19660, #B8941F);
        box-shadow: 0 4px 12px rgba(212, 165, 116, 0.4);
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }
}

</style>
