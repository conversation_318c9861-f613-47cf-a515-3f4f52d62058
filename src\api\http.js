import axios from "axios";
import common from "../util/util";
import sha256 from "js-sha256";
import store from "../store";
// axios.defaults.withCredentials = true;
// axios.defaults.timeout = 10000;

let count = 0;
axios.defaults.timeout = 20000;

axios.interceptors.request.use(
    config => {
        count++;
        common.showLoading();
        if (config.method === 'post' && config.url.indexOf('/upload/file') === -1) {
            config.headers['Content-Type'] = 'application/json'
        } else if (config.url.indexOf('aliyuncs') > -1 || config.url.indexOf('/upload/file') > -1) {
            config.headers['content-type'] = 'application/x-www-form-urlencoded'
        }
        return config;
    },
    error => {
        count--;
        if (count === 0) {
            common.hideLoading();
        }
        return Promise.reject(error);
    }
);

axios.interceptors.response.use(
    response => {
        count--;
        if (count === 0) {
            common.hideLoading();
        }

        if (response.status === 200 && response.data) {
            return response.data;
        } else {
            // let message = getFailureReason();
            // common.showToast(message);
            return Promise.reject(response);
        }
    },
    error => {
        debugger
        count--;
        if (count === 0) {
            common.hideLoading();
        }
        // let message = getFailureReason();
        // common.showToast(message);
        return Promise.reject(error);
    }
);

function getFailureReason(error = "") {
    return error || "网络连接超时，请稍后再试~";
}
function sha256_(timeStamp, nonce) {
    let signingKey = "JlolWcxSD3fTdISQkEURIQ==";
    let salt_ = timeStamp % 10;
    let salt = nonce.substring(salt_);
    let stringSrc = signingKey + timeStamp + nonce + salt;
    return sha256(stringSrc);
}

/**
 * 封装请求
 * @param {String} url [请求的url地址]
 * @param {Object} data [请求参数]
 * @param {String} method [请求方式 get post...]
 * @returns {Promise}
 */
export default (url = "", data = {}, method = "get", header = {}) => {
    let timestamp = new Date().getTime();
    let nonce = common.generateUUID();
    let signature = sha256_(timestamp, nonce);
    // let userId =缓存中获取
    // let userId = common.getInfo().userId
    let userId = window.localStorage.getItem("userId");

    let headers = {
        timestamp: timestamp,
        nonce: nonce,
        signature: signature,
        id: userId,
        'X-Auth-Token': localStorage.getItem('access_token'),
        appType: "3",
        appVersion: "H5",
        logTraceId: nonce,
        remoteChannel: window.sessionStorage.getItem("hlw_remoteChannel") || "all",
        ...header,
    };
    // console.log(url.indexOf('stageBak'))
    // if (url.indexOf("stageBak") < -1) {
    // 展台后管接口
    // headers = {
    //   timestamp: timestamp,
    //   nonce: nonce,
    //   signature: signature,
    //   id: userId,
    //   appType: "3",
    //   appVersion: "H5",
    //   logTraceId: nonce,
    //   remoteChannel: getRemoteChannel(),
    // };
    // }
    // if (url.indexOf("queryBanner") > -1) {
    //   headers = {
    //     logTraceId: nonce,
    //   };
    // }
    return new Promise((resolve, reject) => {
        // url = "https://jsbceshi.hfi-health.com:18188" + url; // 写死为了测试数据
        if (url.indexOf("https://") == -1) {
            // url = "https://jsbceshi.hfi-health.com:18188" + url;
            if (process.env.VUE_APP_RUN_ENV == 'stabletest' && url.indexOf("/inthos/") > -1) {
                url = process.env.VUE_APP_URL + '/rc' + url;
            } else {
                url = process.env.VUE_APP_URL + url;
            }

        } // 为了避免oss上传图片的链接，被拼接      写死为了测试数据
        if (method === "post") {
            axios({
                url,
                method,
                data,
                headers,
                // transformRequest: [
                //   (data) => {
                //     data = qs.stringify(data); // 将对象序列化，以&进行拼接
                //     return data;
                //   },
                // ],
            })
                .then(response => {
                    debugger

                    // 登录失效 重新调用登录
                    if (response.code === "401") {
                        common.loginOut();
                        reject(response.msg)
                    } else if (response.code == "200") {
                        resolve(response);
                    } else {
                        common.showToast(response.msg, 3000);
                        reject(response.msg);
                    }
                })
                .catch(error => {
                    debugger
                    // reject(error);
                    if (error.response && (error.response.status == '503' || error.response.status == '429' || error.response.status == '598')) {
                        common.showToast("活动太火爆啦，请稍后再试~", 3000);
                        reject(error)
                    } else if (error.response && error.response.status == '401') {
                        // 登录失效
                        common.loginOut();
                        reject(error)
                    } else {
                        // 捕获登录
                        reject(error);
                    }

                });
        } else if (method === "get") {
            axios({
                url,
                method,
                params: data,
                headers,
            })
                .then(response => {
                    // 登录失效 重新调用登录
                    if (response.code === "401") {
                        common.loginOut();
                        reject(response.msg)
                    } else if (response.code == "200") {
                        resolve(response);
                    } else {
                        common.showToast(response.msg, 3000);
                        reject(response.msg);
                    }
                })
                .catch(error => {
                    debugger
                    if (error.response && (error.response.status == '503' || error.response.status == '429' || error.response.status == '598')) {
                        common.showToast("活动太火爆啦，请稍后再试~", 3000);
                        reject(error)
                    } else if (error.response && error.response.status == '401') {
                        // 登录失效
                        common.loginOut();
                        reject(error)
                    } else {
                        // 捕获登录
                        reject(error);
                    }
                });
        }
    });
};
