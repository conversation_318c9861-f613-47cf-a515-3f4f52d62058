/**
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @Description: 活动相关API接口
 */

import request from '@/utils/request';
import mockData from './mockData';
import * as homepageApi from './homepage';

// 开关：true使用mock数据，false使用真实API
const USE_MOCK = false;

// 获取Banner列表
export const getBannerList = () => {
  if (USE_MOCK) {
    console.log('调用mock getBannerList');
    return mockData.getBannerList();
  }
  return homepageApi.getBannerList();
};

// 获取精选活动列表
export const getFeaturedActivityList = () => {
  if (USE_MOCK) {
    console.log('调用mock getFeaturedActivityList');
    return mockData.getFeaturedActivityList();
  }
  return homepageApi.getFeaturedActivityList();
};

// 获取活动类型列表
export const getActivityCategoryList = () => {
  if (USE_MOCK) {
    console.log('调用mock getActivityCategoryList');
    return mockData.getActivityCategoryList();
  }
  return homepageApi.getActivityCategoryList();
};

// 1.6 首页的活动列表
export const getActivityList = (params = {}) => {
  if (USE_MOCK) {
    console.log('调用mock getActivityList，参数:', params);
    return mockData.getActivityList(params);
  }
  return homepageApi.getActivityList(params);
};

// 1.5 活动详情
export const getActivityDetail = (actId) => {
  if (USE_MOCK) {
    console.log('调用mock getActivityDetail，参数:', actId);
    return mockData.getActivityDetail(actId);
  }
  return request('/app/activity/detail', { actId }, 'get');
};

// 1.8 首页搜索中间页
export const searchActivity = (params = {}) => {
  if (USE_MOCK) {
    console.log('调用mock searchActivity，参数:', params);
    return mockData.searchActivity(params);
  }
  return homepageApi.searchActivity(params);
};

// 1.7 首页筛选活动
export const chooseActivity = (params = {}) => {
  if (USE_MOCK) {
    console.log('调用mock chooseActivity，参数:', params);
    return mockData.chooseActivity(params);
  }
  return homepageApi.chooseActivity(params);
};

// 1.9 一键报名
export const registerActivity = (data) => {
  if (USE_MOCK) {
    return mockData.registerActivity(data);
  }
  return homepageApi.registerActivity(data);
};
