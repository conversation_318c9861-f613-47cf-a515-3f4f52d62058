/**
 * Vant Swipe 组件补丁
 * 修复轮播组件在页面切换后仍然运行导致的错误
 */

import { Swipe } from 'vant';

// 保存原始方法
const originalMethods = {
  minOffset: null,
  move: null,
  stopAutoplay: null,
  startAutoplay: null,
  destroyed: null
};

// 安全的 minOffset 方法
function safeMinOffset() {
  try {
    // 检查组件是否已销毁或DOM不存在
    if (!this.$el || !this.$el.parentNode || !this.track) {
      console.log('Swipe: 组件已销毁或DOM不存在，跳过minOffset计算');
      return 0;
    }
    
    // 检查track元素是否存在且有宽度
    const trackElement = this.track;
    if (!trackElement || trackElement.offsetWidth === 0) {
      console.log('Swipe: track元素不存在或宽度为0，跳过minOffset计算');
      return 0;
    }
    
    // 调用原始方法
    if (originalMethods.minOffset) {
      return originalMethods.minOffset.call(this);
    }
    
    // 备用计算逻辑
    const { size } = this;
    return size * (this.count - 1) * -1;
  } catch (error) {
    console.error('Swipe minOffset 错误:', error);
    return 0;
  }
}

// 安全的 move 方法
function safeMove(offset, userAction) {
  try {
    // 检查组件是否已销毁
    if (!this.$el || !this.$el.parentNode) {
      console.log('Swipe: 组件已销毁，跳过move操作');
      return;
    }
    
    // 检查必要的属性是否存在
    if (!this.track) {
      console.log('Swipe: track不存在，跳过move操作');
      return;
    }
    
    // 调用原始方法
    if (originalMethods.move) {
      return originalMethods.move.call(this, offset, userAction);
    }
  } catch (error) {
    console.error('Swipe move 错误:', error);
  }
}

// 安全的停止自动播放方法
function safeStopAutoplay() {
  try {
    console.log('Swipe: 停止自动播放');
    
    // 清理定时器
    if (this.autoplayTimer) {
      clearInterval(this.autoplayTimer);
      this.autoplayTimer = null;
    }
    
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    
    // 调用原始方法
    if (originalMethods.stopAutoplay) {
      return originalMethods.stopAutoplay.call(this);
    }
  } catch (error) {
    console.error('Swipe stopAutoplay 错误:', error);
  }
}

// 安全的开始自动播放方法
function safeStartAutoplay() {
  try {
    // 检查组件是否已销毁
    if (!this.$el || !this.$el.parentNode) {
      console.log('Swipe: 组件已销毁，不启动自动播放');
      return;
    }
    
    // 调用原始方法
    if (originalMethods.startAutoplay) {
      return originalMethods.startAutoplay.call(this);
    }
  } catch (error) {
    console.error('Swipe startAutoplay 错误:', error);
  }
}

// 增强的销毁方法
function enhancedDestroyed() {
  try {
    console.log('Swipe: 组件销毁，清理资源');
    
    // 停止自动播放
    this.stopAutoplay();
    
    // 清理所有可能的定时器
    const timerNames = ['autoplayTimer', 'timer', 'touchTimer', 'resizeTimer'];
    timerNames.forEach(timerName => {
      if (this[timerName]) {
        clearInterval(this[timerName]);
        clearTimeout(this[timerName]);
        this[timerName] = null;
      }
    });
    
    // 调用原始销毁方法
    if (originalMethods.destroyed) {
      return originalMethods.destroyed.call(this);
    }
  } catch (error) {
    console.error('Swipe destroyed 错误:', error);
  }
}

// 应用补丁
export function applySwipePatch() {
  console.log('应用 Vant Swipe 组件补丁');
  
  try {
    // 获取 Swipe 组件的原型
    const SwipePrototype = Swipe.prototype || Swipe.options || {};
    
    // 保存原始方法
    if (SwipePrototype.minOffset) {
      originalMethods.minOffset = SwipePrototype.minOffset;
    }
    if (SwipePrototype.move) {
      originalMethods.move = SwipePrototype.move;
    }
    if (SwipePrototype.stopAutoplay) {
      originalMethods.stopAutoplay = SwipePrototype.stopAutoplay;
    }
    if (SwipePrototype.startAutoplay) {
      originalMethods.startAutoplay = SwipePrototype.startAutoplay;
    }
    if (SwipePrototype.destroyed) {
      originalMethods.destroyed = SwipePrototype.destroyed;
    }
    
    // 应用安全方法
    if (SwipePrototype.minOffset) {
      SwipePrototype.minOffset = safeMinOffset;
    }
    if (SwipePrototype.move) {
      SwipePrototype.move = safeMove;
    }
    if (SwipePrototype.stopAutoplay) {
      SwipePrototype.stopAutoplay = safeStopAutoplay;
    }
    if (SwipePrototype.startAutoplay) {
      SwipePrototype.startAutoplay = safeStartAutoplay;
    }
    
    // 添加或增强销毁方法
    SwipePrototype.destroyed = enhancedDestroyed;
    
    console.log('Vant Swipe 组件补丁应用成功');
  } catch (error) {
    console.error('应用 Vant Swipe 组件补丁失败:', error);
  }
}

// 移除补丁（如果需要）
export function removeSwipePatch() {
  console.log('移除 Vant Swipe 组件补丁');
  
  try {
    const SwipePrototype = Swipe.prototype || Swipe.options || {};
    
    // 恢复原始方法
    if (originalMethods.minOffset) {
      SwipePrototype.minOffset = originalMethods.minOffset;
    }
    if (originalMethods.move) {
      SwipePrototype.move = originalMethods.move;
    }
    if (originalMethods.stopAutoplay) {
      SwipePrototype.stopAutoplay = originalMethods.stopAutoplay;
    }
    if (originalMethods.startAutoplay) {
      SwipePrototype.startAutoplay = originalMethods.startAutoplay;
    }
    if (originalMethods.destroyed) {
      SwipePrototype.destroyed = originalMethods.destroyed;
    }
    
    console.log('Vant Swipe 组件补丁移除成功');
  } catch (error) {
    console.error('移除 Vant Swipe 组件补丁失败:', error);
  }
}

// 默认导出
export default {
  applySwipePatch,
  removeSwipePatch
};
