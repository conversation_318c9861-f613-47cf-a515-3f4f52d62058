<template>
  <div class="privacy-page">
    <div class="nav">
      <button class="back" @click="$router.back()">返回</button>
      <h1>隐私协议</h1>
      <div class="spacer"></div>
    </div>
    <div class="content" v-html="content"></div>
  </div>
 </template>

<script>
import { getPrivacyPolicy } from '@/api/auth';

export default {
  name: 'PrivacyPage',
  data() {
    return { content: '' };
  },
  async created() {
    try {
      const result = await getPrivacyPolicy();
      if (result && (result.respCode === '000000' || result.success === 1)) {
        this.content = result.data?.content || '';
      } else {
        this.content = '<p>未获取到隐私协议内容。</p>';
      }
      document.title = '隐私协议';
    } catch (e) {
      this.content = '<p>加载失败，请稍后重试。</p>';
    }
  }
};
</script>

<style scoped>
.privacy-page { min-height: 100vh; background: #fff; }
.nav { position: sticky; top: 0; display: flex; align-items: center; justify-content: space-between; height: 44px; padding: 0 12px; background: #fff; border-bottom: 1px solid #eee; }
.nav .back { border: none; background: none; color: #1083ff; font-size: 14px; }
.nav h1 { margin: 0; font-size: 16px; font-weight: 600; }
.nav .spacer { width: 44px; }
.content { padding: 12px 16px 24px; font-size: 14px; line-height: 1.7; color: #333; }
</style>


