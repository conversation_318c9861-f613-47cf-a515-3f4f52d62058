<template>
  <div class="login-page">
    <!-- 顶部导航栏 -->
    <!-- <div class="top-nav">
      <div class="nav-left">
        <div class="nav-icon back-icon" @click="goBack">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="nav-icon close-icon" @click="goBack">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      <div class="nav-right">
        <div class="nav-icon more-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="1" fill="currentColor"/>
            <circle cx="19" cy="12" r="1" fill="currentColor"/>
            <circle cx="5" cy="12" r="1" fill="currentColor"/>
          </svg>
        </div>
        <div class="nav-icon target-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="12" r="2" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
      </div>
    </div> -->

    <!-- 视觉展示区 -->
    <div class="visual-section">
      <div class="welcome-title">您好，请登录</div>
    </div>

    <!-- 内容卡片 (登录表单) -->
    <div class="content-card">
      <!-- 手机号输入框 -->
      <div class="input-group phone-group">
        <div class="input-container">
          <span class="country-code">+86</span>
          <input
            v-model="phoneNumber"
            type="tel"
            class="phone-input"
            placeholder="请输入手机号码"
            maxlength="13"
            @input="formatPhoneNumber"
            @focus="onPhoneFocus"
            readonly
          />
          <i
            v-if="showClear"
            class="clear-btn"
            @click="clearPhone"
          >×</i>
          <span
            class="get-code-text"
            :class="{ active: canGetCode, disabled: !canGetCode }"
            @click="getVerifyCode"
          >
            {{ codeButtonText }}
          </span>
        </div>
      </div>

      <!-- 验证码输入框 -->
      <div class="input-group code-group">
        <div class="input-container">
          <input
            v-model="verifyCode"
            type="number"
            class="code-input"
            placeholder="请输入短信验证码"
            maxlength="6"
            @focus="onCodeFocus"
            readonly
          />
          <i
            v-if="verifyCode"
            class="clear-btn"
            @click="clearCode"
          >×</i>
        </div>
      </div>

      <!-- 协议同意区 -->
      <div class="privacy-section">
        <label class="checkbox-wrapper circle">
          <input
            v-model="agreePrivacy"
            type="checkbox"
            class="checkbox"
          />
          <span class="checkmark"></span>
          <span class="privacy-text">
            我已阅读并同意<span class="privacy-link" @click="$router.push('/privacy')">《隐私协议名称》</span>
          </span>
        </label>
      </div>

      <!-- 主登录按钮 -->
      <button
        class="login-btn"
        :class="{ active: canLogin, disabled: !canLogin }"
        :disabled="!canLogin"
        @click="handleLogin"
      >
        登录
      </button>

      <!-- 次要登录选项 -->
      <!-- <div class="quick-login">
        <span class="quick-login-text">快捷登录</span>
      </div> -->
    </div>

    <!-- 页面底部提示 -->
    <div class="bottom-tip">
      未注册过的用户将自动创建账号
    </div>

    <!-- 数字键盘 -->
    <div v-if="showKeyboard" class="keyboard-overlay" @click="hideKeyboard">
      <div class="keyboard" @click.stop>
        <div class="keyboard-row">
          <button class="key" @click="inputNumber('1')">1</button>
          <button class="key" @click="inputNumber('2')">2<span class="sub">ABC</span></button>
          <button class="key" @click="inputNumber('3')">3<span class="sub">DEF</span></button>
        </div>
        <div class="keyboard-row">
          <button class="key" @click="inputNumber('4')">4<span class="sub">GHI</span></button>
          <button class="key" @click="inputNumber('5')">5<span class="sub">JKL</span></button>
          <button class="key" @click="inputNumber('6')">6<span class="sub">MNO</span></button>
        </div>
        <div class="keyboard-row">
          <button class="key" @click="inputNumber('7')">7<span class="sub">PQRS</span></button>
          <button class="key" @click="inputNumber('8')">8<span class="sub">TUV</span></button>
          <button class="key" @click="inputNumber('9')">9<span class="sub">WXYZ</span></button>
        </div>
        <div class="keyboard-row">
          <button class="key decimal" @click="inputNumber('.')">.</button>
          <button class="key" @click="inputNumber('0')">0</button>
          <button class="key delete-btn" @click="deleteNumber">⌫</button>
        </div>
      </div>
    </div>

    <!-- 隐私协议弹窗 -->
    <div v-if="showPrivacyModal" class="modal-overlay" @click="hidePrivacyModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>隐私协议</h3>
          <button class="close-btn" @click="hidePrivacyModal">×</button>
        </div>
        <div class="modal-body">
          <div v-html="privacyContent"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { sendSmsCode, getPrivacyPolicy } from '@/api/auth';
import common from '@/util/util';
import auth from '@/utils/auth';
import store from '@/store';
import request from '@/utils/request'; // 导入request实例以获取完整的response对象

export default {
  name: 'LoginPage',
  data() {
    return {
      phoneNumber: '',
      verifyCode: '',
      agreePrivacy: false,
      showKeyboard: false,
      showPrivacyModal: false,
      privacyContent: '',
      countdown: 0,
      countdownTimer: null,
      activeInput: 'phone', // 'phone' or 'code'
      hasSentCode: false, // 是否已经发送过验证码
      showClear: false, // 清除按钮是否显示（仅第一次输入显示）
      hasShownClearOnce: false // 是否已经显示过一次清除按钮
    };
  },
  computed: {
    // 清理后的手机号（去除格式化字符）
    cleanPhoneNumber() {
      return this.phoneNumber.replace(/\s/g, '');
    },
    // 是否可以获取验证码
    canGetCode() {
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0;
    },
    // 是否可以登录
    canLogin() {
      return this.cleanPhoneNumber.length === 11 &&
             this.verifyCode.length === 6 &&
             this.agreePrivacy;
    },
    // 获取验证码按钮文案
    codeButtonText() {
      if (this.countdown > 0) {
        return `${this.countdown}s重新发送`;
      }
      // 如果之前发送过验证码，显示"重新发送"，否则显示"获取验证码"
      return this.hasSentCode ? '重新发送' : '获取验证码';
    }
  },
  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 手机号输入框获得焦点
    onPhoneFocus() {
      this.activeInput = 'phone';
      this.showKeyboard = true;
    },

    // 验证码输入框获得焦点
    onCodeFocus() {
      this.activeInput = 'code';
      this.showKeyboard = true;
    },
    
    // 隐藏键盘
    hideKeyboard() {
      this.showKeyboard = false;
    },
    
    // 格式化手机号显示（3-3-4格式）
    formatPhoneNumber() {
      // 只保留数字
      let value = this.phoneNumber.replace(/\D/g, '');

      // 限制最大长度为11位
      if (value.length > 11) {
        value = value.slice(0, 11);
      }

      // 格式化为3-3-4格式
      if (value.length > 3 && value.length <= 7) {
        value = value.replace(/(\d{3})(\d+)/, '$1 $2');
      } else if (value.length > 7) {
        value = value.replace(/(\d{3})(\d{4})(\d+)/, '$1 $2 $3');
      }

      this.phoneNumber = value;

      // 仅在首次输入时显示清除按钮，之后不再显示
      if (!this.hasShownClearOnce && value.length > 0) {
        this.showClear = true;
      }
    },

    // 清空手机号
    clearPhone() {
      this.phoneNumber = '';
      this.showClear = false;
      this.hasShownClearOnce = true;
    },

    // 清空验证码
    clearCode() {
      this.verifyCode = '';
    },

    // 输入数字
    inputNumber(num) {
      if (this.activeInput === 'phone') {
        if (this.cleanPhoneNumber.length < 11) {
          this.phoneNumber += num;
          this.formatPhoneNumber();
        }
      } else if (this.activeInput === 'code') {
        if (this.verifyCode.length < 6) {
          this.verifyCode += num;
        }
      }
    },
    
    // 删除数字
    deleteNumber() {
      if (this.activeInput === 'phone') {
        // 删除最后一个字符（可能是数字或空格）
        this.phoneNumber = this.phoneNumber.slice(0, -1);
        // 重新格式化
        this.formatPhoneNumber();
      } else if (this.activeInput === 'code') {
        this.verifyCode = this.verifyCode.slice(0, -1);
      }
    },
    
    // 手机号正则校验
    validatePhone(phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },

    // 获取验证码
    async getVerifyCode() {
      if (!this.canGetCode) return;

      const phone = this.cleanPhoneNumber;

      // 手机号校验
      if (!this.validatePhone(phone)) {
        common.showToast('请输入正确的手机号码');
        return;
      }

      try {
        const result = await sendSmsCode(phone);
        // 兼容不同的响应格式
        if (result && (result.respCode === '000000' || result.success === 1 || result.code === 200)) {
          common.showToast('验证码已发送');
          this.hasSentCode = true; // 标记已发送过验证码
          this.startCountdown();
        } else {
          const errorMsg = result?.respMsg || result?.msg || '发送失败，请重试';
          common.showToast(errorMsg);
        }
      } catch (error) {
        console.error('发送验证码失败:', error);
        common.showToast('发送失败，请重试');
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdown = 60;
      this.countdownTimer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer);
          this.countdownTimer = null;
        }
      }, 1000);
    },
    
    // 登录处理
      async handleLogin() {
      if (!this.canLogin) return;
      
      // 检查是否勾选协议
      if (!this.agreePrivacy) {
        common.showToast('请阅读并勾选协议');
        return;
      }

      const phone = this.cleanPhoneNumber;
      const code = this.verifyCode;

      try {
        // 直接使用request实例并设置responseType为'all'以便获取完整的response对象
        const result = await request({
          url: '/appLogin',
          method: 'post',
          data: { mobile: phone, verification: code },
          responseType: 'all'
        });
        
        // 检查响应是否成功 - 兼容多种响应格式
        let responseData = result.data;
        
        // 如果responseData是字符串，需要解析为JSON对象
        if (typeof responseData === 'string') {
          try {
            responseData = JSON.parse(responseData);
          } catch (e) {
            console.error('JSON解析失败:', e);
            common.showToast('响应数据格式错误');
            return;
          }
        }
        
        const isSuccess = responseData && (
          responseData.code === 200
        );
        if (isSuccess) {
          
          // 从响应头中获取token
          const token = result.headers['x-auth-token'] || result.headers['X-Auth-Token'];
          
          if (token) {
            // 保存token到本地存储和store
            auth.setToken(token, true); // 第二个参数为true，保存到localStorage
            localStorage.setItem('access_token', token); // 直接保存到localStorage
            store.commit('SET_TOKEN', token);
          } else {
            console.log('警告: 未获取到token');
          }

          // 保存用户信息
          const userInfo = responseData.data;
          console.log('用户信息:', userInfo);
          if (userInfo) {
            localStorage.setItem('userId', userInfo.userId);
            localStorage.setItem('userInfo', JSON.stringify(userInfo));
            store.commit('SET_USER_INFO', userInfo);
          } else {
            console.log('警告: 未获取到用户信息');
          }

          common.showToast('登录成功');

          // 跳转到来源页面或首页
          // 优先使用sessionStorage中保存的URL，然后是路由参数，最后是首页
          const savedURL = window.sessionStorage.getItem("acURL");
          let redirect = this.$route.query.redirect || '/';
          
          if (savedURL) {
            // 处理保存的URL，确保格式正确
            if (savedURL.startsWith('#')) {
              // 如果是以#开头，去掉#号
              redirect = savedURL.substring(1);
            } else if (savedURL.startsWith('/')) {
              // 如果是以/开头，直接使用
              redirect = savedURL;
            } else {
              // 其他情况，默认使用首页
              redirect = '/';
            }
            
            // 清除保存的URL
            window.sessionStorage.removeItem("acURL");
          }
          
          console.log('登录成功，跳转到:', redirect);
          this.$router.replace(redirect);
        } else {
          // 兼容不同的响应格式
          const errorMsg = responseData?.respMsg || responseData?.msg || '登录失败，请重试';
          common.showToast(errorMsg);
        }

      } catch (error) {
        console.error('登录失败:', error);
        if (error.response) {
          console.log('错误响应状态:', error.response.status);
        }
        common.showToast('登录失败');
      }
    },
    
    // 显示隐私协议
    async showPrivacyPolicy() {
      try {
        const result = await getPrivacyPolicy();
        // 兼容不同的响应格式
        if (result && (result.respCode === '000000' || result.success === 1)) {
          this.privacyContent = result.data?.content || '';
          this.showPrivacyModal = true;
        } else {
          const errorMsg = result?.respMsg || '获取协议内容失败';
          common.showToast(errorMsg);
        }
      } catch (error) {
        console.error('获取隐私协议失败:', error);
        common.showToast('获取协议内容失败');
      }
    },
    
    // 隐藏隐私协议弹窗
    hidePrivacyModal() {
      this.showPrivacyModal = false;
    }
  },
  
  beforeDestroy() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  }
};
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  position: relative;
  background: #f5f5f5;
  overflow: hidden;
}

// 顶部导航栏
.top-nav {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 44px 20px 0;
  z-index: 10;

  .nav-left,
  .nav-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .nav-icon {
    width: 44px;
    height: 44px;
    border-radius: 22px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #333;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

// 视觉展示区
.visual-section {
  height: 326px;
  background: linear-gradient(180deg, #c69e73 0%, #ece0d2 100%);
  background-image: url('@/images/login/编组 2.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 0 36px;
  position: relative;

  .welcome-title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin-top: 60px;
  }
}

// 内容卡片
.content-card {
  position: relative;
  margin-top: -81px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.91) 0%, rgba(255, 255, 255, 1) 100%);
  border-radius: 20px 20px 0 0;
  padding: 40px 28px 60px;
  min-height: calc(100vh - 245px);
  z-index: 5;
}

// 输入框组
.input-group {
  margin-bottom: 20px;

  .input-container {
    height: 50px;
    background: #f4f4f4;
    border-radius: 25px;
    display: flex;
    align-items: center;
    padding: 0 20px;
    position: relative;
    overflow: hidden;
    padding-right: 128px; /* 预留清除与验证码按钮空间 */

    .country-code {
      color: #333;
      font-size: 16px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    .phone-input,
    .code-input {
      flex: 1;
      border: none;
      outline: none;
      background: transparent;
      font-size: 16px;
      color: #333;
      cursor: pointer;

      &::placeholder {
        color: #999;
      }
    }

    .clear-btn {
      position: absolute;
      right: 100px; /* 位于验证码文本左侧 */
      top: 50%;
      transform: translateY(-50%);
      width: 12px;
      height: 12px;
      line-height: 12px;
      font-size: 12px;
      color: #c7c7c7;
      background: transparent;
      border: none;
      border-radius: 0;
      box-shadow: none;
      display: inline-block;
      text-align: center;
      cursor: pointer;
    }

    .get-code-text {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 14px;
      font-family: 'PingFangSC', -apple-system, BlinkMacSystemFont, sans-serif;
      cursor: pointer;
      white-space: nowrap;
      text-align: center;
      line-height: 20px;
      max-width: 96px;
      overflow: hidden;
      text-overflow: ellipsis;

      &.disabled {
        color: rgb(155, 155, 155);
        cursor: not-allowed;
      }

      &.active {
        color: rgb(16, 131, 255);
        cursor: pointer;
      }
    }
  }
}

// 协议同意区
.privacy-section {
  margin: 30px 0;
  padding-left: 27px;

  .checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;

    .checkbox {
      display: none;
    }

    .checkmark {
      width: 16px;
      height: 16px;
      border: 1px solid #ddd;
      border-radius: 3px;
      margin-right: 12px;
      position: relative;
      flex-shrink: 0;

      &::after {
        content: '';
        position: absolute;
        left: 4px;
        top: 1px;
        width: 6px;
        height: 10px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
        opacity: 0;
      }
    }

    .checkbox:checked + .checkmark {
      background: #007aff;
      border-color: #007aff;

      &::after {
        opacity: 1;
      }
    }

    .privacy-text {
      font-size: 14px;
      color: #282828;

      .privacy-link {
        color: #1083ff;
        text-decoration: none;
        cursor: pointer;
      }
    }
  }

  /* 圆形勾选框样式 */
  .checkbox-wrapper.circle {
    .checkmark {
      width: 14px; /* 外圈尺寸 */
      height: 14px;
      border-radius: 50%;
      background: transparent;
      box-sizing: border-box;
    }
    /* 内部小圆点（默认隐藏） */
    .checkmark::after {
      content: '';
      position: absolute;
      width: 8px; /* 内部圆点尺寸 */
      height: 8px;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      background: #007aff;
      border: none; /* 清除基础样式里的对勾边框 */
      box-shadow: none;
      opacity: 0;
    }
    /* 选中：外圈变蓝，显示内部蓝色小圆点 */
    .checkbox:checked + .checkmark {
      background: transparent;
      border-color: #007aff;
    }
    .checkbox:checked + .checkmark::after { opacity: 1; }
  }
}

// 主登录按钮
.login-btn {
  width: 100%;
  height: 50px;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  background: linear-gradient(90deg, #c08a48 0%, #edc391 100%);
  color: #fff;
  margin-bottom: 20px;

  &.disabled {
    background: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }

  &.active {
    background: linear-gradient(90deg, #c08a48 0%, #edc391 100%);
    color: #fff;
  }
}

// 次要登录选项
.quick-login {
  text-align: center;
  margin-bottom: 40px;

  .quick-login-text {
    font-size: 16px;
    color: #666;
    cursor: pointer;
    text-decoration: underline;
  }
}

// 页面底部提示
.bottom-tip {
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 12px;
  color: #c1c1c1;
  padding: 0 20px;
  line-height: 16.5px;
  font-family: 'PingFangSC', -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
  z-index: 6; /* 高于内容卡片，确保显示 */
}

// 数字键盘样式
.keyboard-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.keyboard {
    width: 100%;
    background: #e6e6e6;
    border-top: 1px solid #c8c7cc;
    padding: 8px 8px 40.5pt 8px; /* 调整底部间距为40.5pt */
    border-radius: 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    animation: slideUp 0.3s ease-out;
    transform-origin: bottom center;
  }

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.keyboard-row {
  display: flex;
  justify-content: center;
  margin-bottom: 8px; /* 行与行之间的间距 */
  height: 44px; /* 再扁一点的按键高度 */
}

.keyboard-row:last-child {
  margin-bottom: 0;
}

.key {
  flex: 1;
  background: #ffffff;
  border: none;
  border-radius: 8px; /* 轻微的圆角 */
  font-size: 23px;
  font-weight: 500; /* 黑粗字体 */
  color: #000000;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 3px;
  margin: 0 4px; /* 按键之间的间距 */
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
  user-select: none;
  -webkit-user-select: none;
  position: relative;
  transition: background-color 0.15s ease;
  max-width: calc(33.333% - 8px); /* 确保按键不会太宽 */
}

.key:active {
  background-color: #d1d1d6;
}

.key.empty {
  background-color: transparent;
  cursor: default;
}

.key.empty:active {
  background-color: transparent;
}

/* 小数点键样式 - 与背景融为一体 */
.key.decimal {
  background-color: #e6e6e6;
  border-radius: 0;
  margin: 0;
  padding-top: 6px;
}

.key.decimal:active {
  background-color: #d1d1d6;
}

/* 删除键样式 - 与背景融为一体 */
.key.delete-btn {
  background-color: #e6e6e6;
  border-radius: 0;
  margin: 0;
  padding-top: 6px;
}

.key.delete-btn:active {
  background-color: #d1d1d6;
}

.key .sub {
  font-size: 10px;
  color: #000000;
  position: absolute;
  bottom: 4px; /* 减小bottom值，增加与数字的距离 */
  font-weight: 500;
  text-transform: uppercase;
}

/* 确保数字和字母正确显示 */
.key span {
  display: block;
  text-align: center;
}

/* 验证码按钮样式 - 获取验证码 */
.get-code-btn {
  color: #1083ff; /* RGB(16, 131, 255) */
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', sans-serif;
  line-height: 44px;
  height: 20px;
  width: 70px;
  text-align: left;
  background: transparent;
  border: none;
  padding: 0;
}

/* 验证码按钮样式 - 重新发送 */
.get-code-btn:contains('重新发送') {
  width: 56px;
  text-align: center;
}

.get-code-btn.disabled {
  color: #ccc;
}

/* 验证码输入框样式 */
.verification-code {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  border-bottom: 1px solid #e5e5e5;
}

.verification-code .input-wrapper {
  flex: 1;
  padding: 12px 0;
}

.verification-code input {
  font-size: 16px;
  color: #000;
  width: 100%;
  background: none;
  border: none;
  outline: none;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
}
// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  background: #fff;
  border-radius: 12px;
  max-width: 90%;
  max-height: 80%;
  overflow: hidden;

  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
    }

    .close-btn {
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      font-size: 20px;
      cursor: pointer;
      color: #666;
    }
  }

  .modal-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
  }
}

// 响应式设计
@media (max-width: 375px) {
  .visual-section {
    .welcome-title {
      font-size: 24px;
    }
  }

  .content-card {
    padding: 32px 20px 50px;
  }

  .top-nav {
    padding: 44px 16px 0;

    .nav-icon {
      width: 40px;
      height: 40px;
      border-radius: 20px;
    }
  }
}

@media (min-width: 414px) {
  .content-card {
    padding: 50px 40px 70px;
  }
}

// 动画效果
.content-card {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.login-btn {
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.input-container {
  transition: all 0.3s ease;

  &:focus-within {
    background: #ebebeb;
    transform: translateY(-1px);
  }
}

// 自定义Toast样式 - 覆盖Vant默认样式
:deep(.van-toast) {
  width: 223.5px;
  height: 54.5px;
  background: rgba(0, 0, 0, 1) !important;
  border-radius: 27.5px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;

  .van-toast__text {
    font-size: 14px !important;
    font-family: 'PingFangSC', -apple-system, BlinkMacSystemFont, sans-serif !important;
    color: rgba(255, 255, 255, 1) !important;
    text-align: center !important;
    line-height: 20px !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

// 兼容旧版本的mint-ui toast样式
:deep(.mint-toast) {
  width: 223.5px !important;
  height: 54.5px !important;
  background: rgba(0, 0, 0, 1) !important;
  border-radius: 27.5px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;

  .mint-toast-text {
    font-size: 14px !important;
    font-family: 'PingFangSC', -apple-system, BlinkMacSystemFont, sans-serif !important;
    color: rgba(255, 255, 255, 1) !important;
    text-align: center !important;
    line-height: 20px !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}
</style>
