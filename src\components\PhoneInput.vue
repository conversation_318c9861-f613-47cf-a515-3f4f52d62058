<template>
  <div class="phone-input-wrapper">
    <div class="input-container">
      <span class="country-code">+86</span>
      <input
        v-model="localPhoneNumber"
        type="tel"
        class="phone-input"
        placeholder="请输入手机号码"
        @focus="onFocus"
        readonly
      />
      <!-- <button
        v-if="cleanPhoneNumber.length"
        class="clear-btn"
        type="button"
        aria-label="清除"
        @click="clearPhone"
      >
        ×
      </button> -->
      <button
        class="get-code-btn"
        :class="codeButtonClass"
        :disabled="!canGetCode"
        @click="handleGetCode"
      >
        {{ codeButtonText }}
      </button>
    </div>
  </div>
</template>

<script>
import common from '@/util/util';

export default {
  name: 'PhoneInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    countdown: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      localPhoneNumber: this.value,
      hasSentCode: false // 跟踪是否已经发送过验证码
    };
  },
  computed: {
    cleanPhoneNumber() {
      return this.localPhoneNumber.replace(/\s/g, '');
    },
    canGetCode() {
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0;
    },
    codeButtonText() {
      if (this.countdown > 0) {
        return `${this.countdown}s重新发送`;
      }
      // 只有在已经发送过验证码且倒计时为0时才显示"重新发送"
      return this.cleanPhoneNumber.length === 11 && this.countdown === 0 && this.hasSentCode ? '重新发送' : '获取验证码';
    },
    
    // 获取验证码按钮的样式类
    codeButtonClass() {
      if (this.countdown > 0) {
        return 'countdown';
      }
      if (this.cleanPhoneNumber.length === 11 && this.countdown === 0 && this.hasSentCode) {
        return 'resend';
      }
      return 'default';
    }
  },
  watch: {
    value(newVal) {
      this.localPhoneNumber = newVal;
    },
    localPhoneNumber(newVal) {
      this.formatPhoneNumber(newVal);
    }
  },
  methods: {
    // 格式化手机号显示（3-3-4格式）
    formatPhoneNumber(value) {
      let formattedValue = value.replace(/\D/g, '');
      if (formattedValue.length > 11) {
        formattedValue = formattedValue.slice(0, 11);
      }
      
      if (formattedValue.length > 3 && formattedValue.length <= 7) {
        formattedValue = formattedValue.replace(/(\d{3})(\d+)/, '$1 $2');
      } else if (formattedValue.length > 7) {
        formattedValue = formattedValue.replace(/(\d{3})(\d{4})(\d+)/, '$1 $2 $3');
      }
      
      if (formattedValue !== this.localPhoneNumber) {
        this.localPhoneNumber = formattedValue;
        this.$emit('input', formattedValue);
      }
    },
    
    // 手机号正则校验
    validatePhone(phone) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },
    
    // 处理获取验证码
    handleGetCode() {
      if (!this.canGetCode) return;
      
      const phone = this.cleanPhoneNumber;
      
      // 手机号校验
      if (!this.validatePhone(phone)) {
        common.showToast('请输入正确的手机号码');
        return;
      }
      
      // 标记已经发送过验证码
      this.hasSentCode = true;
      
      this.$emit('get-code', phone);
    },
    
    // 输入框获得焦点
    onFocus() {
      this.$emit('focus');
    },
    
    // 清空手机号
    clearPhone() {
      this.localPhoneNumber = '';
      this.$emit('input', '');
    }
  }
};
</script>

<style scoped>
.phone-input-wrapper {
  margin-bottom: 16px;
}

.input-container {
  display: flex;
  align-items: center;
  position: relative;
  width: 318px;
  height: 50.5px;
  background-color: #F4F4F4;
  border-radius: 27.5px;
  padding: 0 20px;
  box-sizing: border-box;
  overflow: hidden; /* 防止内部绝对定位元素溢出 */
}

.country-code {
  color: #4C4C4C;
  font-size: 15px;
  margin-right: 10px;
  margin-left: 5px;
}

.phone-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #9B9B9B;
  background: transparent;
  padding-right: 124px; /* 预留清除与按钮空间 */
}

.phone-input::placeholder {
  color: #999;
}

.get-code-btn {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  font-size: 13px;
  font-family: 'PingFangSC', 'Helvetica Neue', Arial, sans-serif;
  padding: 0;
  cursor: pointer;
  text-align: center;
  opacity: 1.0;
  white-space: nowrap;
  overflow: hidden;
  max-width: 96px; /* 限制按钮最大宽度，避免溢出 */
  appearance: none;
  -webkit-appearance: none;
  border-radius: 0;
  box-shadow: none;
  z-index: 2;
}

.get-code-btn.default {
  color: #9B9B9B;
  width: auto;
}

.get-code-btn.active { color: #1083FF; width: auto; }

.get-code-btn.resend {
  color: #1083FF;
  width: auto;
}

.get-code-btn.countdown {
  color: #9B9B9B;
  width: auto;
}

.get-code-btn:disabled {
  opacity: 1;
  cursor: default;
}

/* 更小的清除按钮，仅显示一个 x，无圆圈 */
.clear-btn {
  position: absolute;
  right: 100px; /* 位于获取验证码按钮左侧 */
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  padding: 0;
  width: 12px;
  height: 12px;
  line-height: 12px;
  text-align: center;
  font-size: 12px;
  color: #C7C7C7;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  border-radius: 0;
  box-shadow: none;
  z-index: 2;
}
.clear-btn::before { content: '×'; }
.clear-btn:focus { outline: none; }
.clear-btn:focus { outline: none; }
</style>