<template>
    <div
        style="
            width: 100%;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            background: #faf9f7;
        "
    >
        <!-- 头部用户信息 -->
        <div class="header">
            <van-tabs class="statusBar" @click="choseS">
                <van-tab v-for="(it, index) in tabs" :key="index">
                    <template #title>
                        <span class="item">{{ it.txt }} </span>
                    </template>
                </van-tab>
            </van-tabs>

            <!-- <div class="statusBar">
                <span
                    class="item"
                    :class="{ active: status == 2 }"
                    @click="choseS('2')"
                    >进行中</span
                >
                <span
                    class="item"
                    :class="{ active: status == 3 }"
                    @click="choseS('3')"
                    >已结束/已下架</span
                >
            </div> -->
            <div class="ingStatusBar" v-show="status == 2">
                <span
                    class="item"
                    :class="{ active: oStatus == 'all' }"
                    @click="choseOS('all')"
                    >全部</span
                >
                <span
                    class="item"
                    :class="{ active: oStatus == '0' }"
                    @click="choseOS('0')"
                    >待签到</span
                >
                <span
                    class="item"
                    :class="{ active: oStatus == '1' }"
                    @click="choseOS('1')"
                    >取消报名</span
                >
            </div>
        </div>
        <div
            class="list"
            v-if="list.length > 0"
            :style="`${
                status == 2 ? 'margin-top:0.9rem' : 'margin-top:0.57rem'
            }`"
        >
            <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    finished-text="我也是有底线的~"
                    @load="onLoad"
                    :immediate-check="false"
                >
                    <div
                        v-for="(item, index) in list"
                        :key="index"
                        class="listItem"
                    >
                        <div class="top">
                            <span>报名者：{{ item.registerName }}</span>
                            <span :class="getSc(item.registerStatus)">{{
                                item.registerStatus
                            }}</span>
                        </div>

                        <div class="line"></div>

                        <div class="cont">
                            <div class="left">
                                <img
                                    :src="item.headerImg"
                                    @error="handleErrImg"
                                />
                            </div>
                            <div class="right">
                                <div class="acName">
                                    {{ item.actTitle }}
                                </div>

                                <div class="acTime">
                                    {{ item.actTime }}
                                </div>
                            </div>
                        </div>
                    </div>
                </van-list>
            </van-pull-refresh>
        </div>

        <div class="empty" v-if="list.length == 0">
            <img src="@/images/activity/empty.png" />
            <p>暂未查询到信息～</p>
        </div>
    </div>
</template>

<script>
import Vue from "vue";
import { Dialog, Toast, Uploader, List, PullRefresh, Tab, Tabs } from "vant";
import { getMyActivities } from "@/api/my";
import util from "@/util/util";
Vue.use(Toast).use(Uploader).use(List).use(PullRefresh);
Vue.use(Tab);
Vue.use(Tabs);

// 活动状态ActivityStatusEnum
// 3	活动已结束
// 1	活动未开始
// 2	活动进行中

// 订单状态OrderStatusEnum
// 0	待签到
// 1	已取消
// 2	已签到
export default {
    name: "Profile",
    data() {
        return {
            tabs: [
                { id: "2", txt: "进行中" },
                { id: "3", txt: "已结束" },
            ],
            userInfo: { avatar: "" },
            activityStats: {
                registered: 0,
                participated: 0,
                favorite: 0,
            },
            status: "2",
            oStatus: "all",
            list: [],
            loading: false,
            finished: false,
            refreshing: false,
            pageNum: 1,
            pageSize: 10,
            // 默认值
            defaultImage: require("@/images/activity/empty.png"),
        };
    },
    mounted() {
        this.getList();
    },
    methods: {
        handleErrImg(event) {
            event.target.src = this.defaultImage;
            // 控制不要一直跳动
            event.target.onerror = null;
        },
        getSc(txt) {
            if (txt.indexOf("已签到") > -1) {
                return "statusYQD";
            } else if (txt.indexOf("待签到") > -1) {
                return "statusDQD";
            } else {
                return "";
            }
        },
        onLoad() {
            if (this.refreshing) {
                this.list = [];
                this.refreshing = false;
                this.pageNum = 1;
            }

            this.getList();
        },
        onRefresh() {
            // 清空列表数据
            this.finished = false;

            // 重新加载数据
            // 将 loading 设置为 true，表示处于加载状态
            this.loading = true;
            this.onLoad();
        },
        choseS(index, t) {
            let os = this.tabs[index].id;
            debugger;
            if (os == this.status) {
                return;
            }
            this.status = os;
            this.getList(1);
        },
        // choseS(os) {
        //     if (os == this.status) {
        //         return;
        //     }
        //     this.status = os;
        //     this.getList(1);
        // },
        choseOS(os) {
            if (os == this.oStatus) {
                return;
            }
            this.oStatus = os;
            this.getList(1);
        },
        isLogin() {
            if (this.userInfo.userId) return true;
            this.gologin();
            return false;
        },
        getList(isNew) {
            // 需要重置页数和list
            if (isNew) {
                this.pageNum = 1;
                this.list = [];
                this.finished = false;
            }

            let data = {
                pageNum: this.pageNum,
                pageSize: this.pageSize,
                activityStatus: this.status,
            };
            if (this.status == 2) {
                data.orderStatus = this.oStatus == "all" ? "" : this.oStatus;
            }
            getMyActivities(data).then((r) => {
                if (r && r.code == 200 && r.data) {
                    this.loading = false;
                    if (r.data.pages <= this.pageNum) {
                        // 判断下是否为最后一页
                        // 总页数等于或超过当前页
                        this.finished = true;
                    } else {
                        this.pageNum++;
                    }
                    console.log("rrr", r.data);

                    this.list = this.list.concat(r.data.list);
                }
            });
        },
    },
};
</script>

<style lang="less" scoped>
::v-deep .van-tabs--line .van-tabs__wrap {
    width: 100%;
}
::v-deep .van-tabs__nav {
    background: none;
}

::v-deep .van-tabs__line {
    display: inline-block;
    content: "";
    width: 20px;
    height: 3px;
    background: linear-gradient(90deg, #c08a48 0%, #e2b279 100%), #c79252;
    border-radius: 2px;
    margin-top: 6px;
}

::v-deep .van-tabs--line .van-tabs__wrap {
    height: 0.3rem;
}

.header .statusBar .van-tab--active .item {
    color: transparent;
    background: linear-gradient(0deg, #c08a48 0%, #e2b279 100%);
    // background: linear-gradient(45deg, red, yellow);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 600;
    font-size: 16px;
}
.header {
    width: 100%;
    // height: 92px;
    background: linear-gradient(348deg, #ffffff 0%, #efdfcb 100%);
    position: fixed;
    z-index: 1;
    .statusBar {
        width: 100%;
        // height: 0.35rem;
        display: flex;
        justify-content: center;
        align-items: flex-start;
        padding-top: 0.15rem;
        padding-bottom: 0.12rem;
        .item {
            font-weight: 500;
            font-size: 14px;
            color: #292929;
            width: 50%;
            text-align: center;
        }
        .active.item {
            color: transparent;
            background: linear-gradient(0deg, #c08a48 0%, #e2b279 100%);
            // background: linear-gradient(45deg, red, yellow);
            -webkit-background-clip: text;
            color: transparent;
            font-weight: 600;
            font-size: 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .active::after {
            display: inline-block;
            content: "";
            width: 20px;
            height: 3px;
            background: linear-gradient(90deg, #c08a48 0%, #e2b279 100%),
                #c79252;
            border-radius: 2px;
            margin-top: 6px;
        }
    }
    .ingStatusBar {
        width: 100%;
        display: flex;
        align-items: center;
        padding-bottom: 6px;
        padding-left: 11px;
        .item {
            display: block;
            width: 65px;
            height: 27px;
            line-height: 27px;
            background: #ffffff;
            border-radius: 4px;
            font-weight: 400;
            font-size: 11px;
            color: #4e4e4e;
            margin-right: 8px;
            text-align: center;
        }
        .item.active {
            background: #fffaf7;
            border-radius: 4px;
            border: 0px solid #e7c59a;
            color: #a8722f;
        }
    }
}

.empty {
    font-weight: 500;
    font-size: 14px;
    color: #cfcfcf;
    padding-top: 160px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
        width: 197px;
        height: 140px;
    }
    p {
        margin-top: 14px;
        margin-bottom: 0.2rem;
    }
}

.list {
    margin-top: 92px;
    height: calc(100% - 100px);
    .listItem {
        width: 351px;
        min-height: 132px;
        background: #ffffff;
        border-radius: 6px;
        margin: 10px auto 0;

        padding-left: 17px;
        padding-right: 17px;
        box-sizing: border-box;

        .top {
            font-size: 11px;
            color: #ababab;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 8px;
            padding-bottom: 8px;
            .statusYQD {
                color: #07c160;
            }

            .statusDQD {
                color: #c79353;
            }
        }

        .line {
            width: 315px;
            height: 1px;
            background: #f0f0f0;
        }

        .cont {
            margin-top: 14px;
            margin-bottom: 14px;
            display: flex;

            .left {
                width: 73px;
                height: 73px;
                background: #d8d8d8;
                border-radius: 4px;
                margin-right: 15px;

                display: flex;
                justify-content: center; /* 水平居中 */
                align-items: center; /* 垂直居中 */

                img {
                    max-width: 100%; /* 保持图片比例 */
                    max-height: 100%; /* 保持图片比例 */
                }
            }

            .right {
                width: 225px;
                .acName {
                    font-weight: 500;
                    font-size: 14px;
                    color: #373737;
                    white-space: nowrap; /* 不换行 */
                    overflow: hidden; /* 隐藏超出的内容 */
                    text-overflow: ellipsis; /* 用省略号表示被隐藏的部分 */
                    width: 100%; /* 设置最大宽度以限制文本的显示长度 */
                }
                .acTime {
                    margin-top: 4px;
                    font-size: 10px;
                    color: #ababab;
                }
            }
        }
    }
}
</style>
