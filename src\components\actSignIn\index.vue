
<template>
    <van-overlay :show="show" :z-index="999" class="signLay">
        <div class="listBox" v-if="showList">
            <div class="title">
                <p>扫码签到</p>
                <p class="tip">请选择活动，扫描现场活动签到码完成签到</p>
            </div>
            <div class="signIcon"></div>

            <div @click.stop class="listItemBox">
                <!-- 活动列表 -->
                <div
                    v-for="(item, index) in list"
                    :key="index"
                    class="listItem"
                    :class="{ active: acId == item.id }"
                >
                    <div class="cont">
                        <div class="left">
                            <img :src="item.headerImg" @error="handleErrImg" />
                        </div>
                        <div class="right">
                            <div class="acName">
                                {{ item.actTitle }}
                            </div>

                            <div class="choseBtn" @click="choseThis(item)">
                                选这个
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="btns">
                <div class="btnLeft" @click="showSingleSign">录入签到码</div>
                <div class="btnRight">扫码签到</div>
            </div>
        </div>

        <div class="wrapper" @click.stop v-if="showSingle">
            <!-- 单个活动的录入 -->
            <p class="title">请输入活动<span class="qdm">签到码</span></p>

            <input class="qdmInput" placeholder="请输入" v-model="qdm" />

            <div class="suBtn" @click="signAc" :class="{ disabled: !qdm }">
                确认签到
            </div>
        </div>

        <div class="closeIcon" @click="closeDia"></div>
    </van-overlay>
</template>
  
  <script>
import tools from "@/util/tools";
import util from "@/util/util";
import common from "@/util/common";
import { getsignAct } from "@/api/my";
import Vue from "vue";
import { Overlay } from "vant";
Vue.use(Overlay);

export default {
    data() {
        return {
            origin: "",
            show: false,
            showList: false,
            showSingle: false,
            acId: "",
            list: [],
            qdm: "",
            defaultImage: require("@/images/activity/empty.png"),
            mode: "", //空值-活动列表  scan--扫码  input--输入
        };
    },
    props: {
        doctorList: {
            type: Array,
            default: () => [],
        },
        // 医生.doctor-info展示样式自定义
        doctorStyle: {
            type: Object,
            default: () => {},
        },
        // 是否为复诊开方
        repeatVisit: {
            type: Boolean,
            default: () => false,
        },
        // 是否为在线咨询
        onlineVisit: {
            type: Boolean,
            default: () => false,
        },
    },
    watch: {
        doctorList: {
            handler(newVal) {
                console.log("wtach=====", newVal);
                this.doctorList = newVal;
            },
        },
    },
    mounted() {},
    methods: {
        signAc() {},
        showSingleSign() {
            this.showList = false;
            this.showSingle = true;
            this.qdm = "";
        },
        choseThis(item) {
            // todo
            this.acId = item.id;
        },
        closeDia() {
            if (this.showSingle) {
                // 输入签到码模式
                this.showSingle = false;
                if (this.mode == "input") {
                    this.show = false;
                } else {
                    this.showList = true;
                }
            } else {
                // todo
                this.show = false;
                this.showSingle = false;
                this.acId = "";
                this.list = [];
            }
        },
        handleErrImg(event) {
            event.target.src = this.defaultImage;
            // 控制不要一直跳动
            event.target.onerror = null;
        },
        showDia(acId, type) {
            // 支持显示待签到的活动列表  或者  是录入弹窗  扫码签到（微信小程序中）
            // 如果是录入弹窗  需要传活动id
            this.show = true;
            if (acId) {
                // 进入签到-录入或者扫码
                this.mode = type || "input";
                this.qdm = "";
                this.acId = acId;
                this.showSingle = true;
            } else {
                // 活动列表
                this.getList();
            }
        },
        getList() {
            getsignAct({}).then((r) => {
                if (r && r.code == 200 && r.data) {
                    this.list = r.data;
                    if (this.list.length > 0) {
                        // 显示活动列表页
                        this.showList = true;
                        // 默认选中第一个活动
                        this.acId = this.list[0].id;
                    } else {
                        // todo 提示没有可签到的活动 弹窗提示
                    }
                }
            });
        },
        // 点击预约挂号的跳转  需要判断是卫健还是自建平台   如果在卫健中，跳转到我们的小程序 my.postMessage
        //  如果是
        async gotoYYGH(item) {
            // docName 为医生的姓名
            let docName = item.doctorName;
            let url =
                "https://www.hfi-health.com:28181/appointWithDoc/#/search?origin=hlwywMini&cont=" +
                docName;
            let params = {
                hospId: item.hospId ? item.hospId : "",
                doctorId: item.doctorId,
            };
            let isjump = await doctorScheduleByHospidAndDoctorId(params);
            if (!isjump) {
                util.openDialogAlert("", "该医生暂无预约挂号排班");
                return;
            }
            if (this.$store.state.isWjAliMini) {
                // 卫健
                let jumpUrl = `/pages/index/index?returnURL=${encodeURIComponent(
                    url
                )}`;
                my.postMessage({
                    action: "gotoMini",
                    appId: "2021002138635948",
                    url: jumpUrl,
                    authStatus: "2",
                });
            } else {
                // 自建平台
                tools.jumpUrlManage({
                    status: "2",
                    jumpUrl: url,
                });
            }
        },
    },
};
</script>
  
<style lang="less" scoped>
.signLay {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
        font-weight: 600;
        font-size: 27px;
        color: #292929;
        padding-top: 32px;
        padding-left: 23px;
        .tip {
            font-size: 12px;
            color: #919191;
            font-weight: 400;
            margin-top: 5px;
        }
    }
}
.listBox {
    width: 287px;
    min-height: 364px;

    background: linear-gradient(180deg, #f3e5d2 0%, rgba(255, 255, 255, 0) 100%),
        #ffffff;
    box-shadow: inset 1px 2 0px 0px #ffffff;
    border-radius: 12px;
    position: relative;

    .signIcon {
        position: absolute;
        right: 0rem;
        width: 1.55rem;
        height: 0.8rem;
        background: url("@/images/activity/acSign.png") no-repeat center;
        background-size: 100%;
        top: -25px;
    }
    .listItemBox {
        min-height: 184px;
        max-height: calc(70vh - 140px);
        // max-height: calc(50vh - 140px);
        overflow-y: auto;
    }
    .listItem {
        width: 2.41rem;
        // min-height: 0.82rem;
        background: #faf9f7;
        border-radius: 6px;
        margin: 7px auto 0;
        padding: 7px;
        box-sizing: border-box;

        .cont {
            display: flex;
            .left {
                width: 69px;
                height: 69px;
                background: #d8d8d8;
                border-radius: 4px;
                margin-right: 15px;
                flex-shrink: 0;

                display: flex;
                justify-content: center; /* 水平居中 */
                align-items: center; /* 垂直居中 */

                img {
                    max-width: 100%; /* 保持图片比例 */
                    max-height: 100%; /* 保持图片比例 */
                }
            }

            .right {
                // width: 225px;
                flex-grow: 1;
                position: relative;
                .acName {
                    font-weight: 500;
                    font-size: 11px;
                    color: #323232;
                }

                .choseBtn {
                    position: absolute;
                    right: 5px;
                    bottom: 0;

                    width: 50px;
                    height: 21px;
                    line-height: 21px;
                    text-align: center;
                    background: #f0e8de;
                    border-radius: 3px;
                    border: 0px solid #c7975e;
                    font-weight: 400;
                    font-size: 9px;
                    color: #a7773d;
                }
            }
        }
    }

    .listItem.active {
        border: 1px solid #cf892a;
    }

    .btns {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 15px 0;
        background: #fff;
        border-radius: 0 0 12px 12px;
        .btnLeft {
            width: 112px;
            height: 38px;
            border-radius: 6px;
            border: 1px solid #b7b7b7;
            font-weight: 500;
            font-size: 14px;
            color: #383838;
            line-height: 0.38rem;
            text-align: center;
            margin-right: 17px;
        }

        .btnRight {
            width: 112px;
            height: 38px;
            background: linear-gradient(90deg, #c08a48 1%, #edc391 100%),
                #e0c9c9;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            line-height: 0.38rem;
            text-align: center;
        }
    }
}

.wrapper {
    width: 287px;
    height: 217px;
    background: linear-gradient(180deg, #f3e5d2 0%, rgba(255, 255, 255, 0) 100%),
        #ffffff;
    box-shadow: inset 1px 2 0px 0px #ffffff;
    border-radius: 12px;
    .title {
        font-weight: 600;
        font-size: 20px;
        color: #292929;
        .qdm {
            color: #c3863d;
        }
    }
    .qdmInput {
        width: 241px;
        height: 50px;
        background: #faf9f7;
        border-radius: 4px;
        margin-left: 23px;
        margin-top: 16px;
        padding: 6px 20px;
        box-sizing: border-box;
        font-weight: 400;
        font-size: 13px;
        color: #292929;
    }

    .qdmInput::placeholder {
        color: #292929;
    }

    .suBtn {
        width: 240px;
        height: 40px;
        background: linear-gradient(90deg, #c08a48 1%, #edc391 100%), #e0c9c9;
        border-radius: 6px;
        font-size: 14px;
        margin: 20px auto;
        font-weight: 500;
        color: #ffffff;
        text-align: center;
        line-height: 40px;
    }
    .suBtn.disabled {
        background: #f3d5b0;
    }
}
.closeIcon {
    width: 24px;
    height: 24px;
    background: url("@/images/activity/closeIcon.png") no-repeat center;
    background-size: 100%;
    margin-top: 22px;
}
</style>
