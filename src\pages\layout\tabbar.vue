<template>
  <div class="layout-container">
    <!-- 主内容区域 -->
    <div class="main-content">
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive" />
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath" />
    </div>
    
    <!-- 底部导航栏 -->
    <van-tabbar v-model="activeTab" @change="handleTabChange" fixed>
      <van-tabbar-item name="home">
        <template #icon="props">
          <img :src="props.active ? require('@/assets/images/ziyingAct.png') : require('@/assets/images/ziyingNor.png')" />
        </template>
        活动主页
      </van-tabbar-item>
      <van-tabbar-item name="profile">
        <template #icon="props">
          <img :src="props.active ? require('@/assets/images/personAct.png') : require('@/assets/images/personNor.png')" />
        </template>
        个人中心
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>
import Vue from 'vue';
import { Tabbar, TabbarItem } from 'vant';

Vue.use(Tabbar).use(TabbarItem);

export default {
  name: 'LayoutTabbar',
  data() {
    return {
      activeTab: 'home'
    };
  },
  watch: {
    $route: {
      handler(to) {
        // 根据路由更新当前激活的tab
        if (to.name === 'home') {
          this.activeTab = 'home';
        } else if (to.name === 'profile') {
          this.activeTab = 'profile';
        }
      },
      immediate: true
    }
  },
  methods: {
    handleTabChange(name) {
      if (name !== this.$route.name) {
        this.$router.push({ name });
      }
    }
  }
};
</script>

<style lang="less" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow: hidden;
  padding-bottom: 50px; // 为底部导航栏留出空间
}

// 底部导航栏样式
::v-deep .van-tabbar {
  height: 66px;
  
  .van-tabbar-item {
    .van-tabbar-item__icon {
      img {
        width: 24px;
        height: 24px;
      }
    }
    
    .van-tabbar-item__text {
      font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 10px;
      font-weight: 400; // Regular
      line-height: 14px;
      text-align: center;
      margin-top: 3px; // 距离图标3pt
    }
    
    // 未选中状态
    &.van-tabbar-item--inactive {
      .van-tabbar-item__text {
        color: #999999;
      }
    }
    
    // 选中状态
    &.van-tabbar-item--active {
      .van-tabbar-item__text {
        color: #C18B4A;
      }
    }
  }
}

// 移除默认边框
::v-deep .van-hairline--top-bottom::after,
.van-hairline-unset--top-bottom::after {
  border-width: 0;
}
</style>
