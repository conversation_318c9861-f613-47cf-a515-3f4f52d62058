/*
 * @Author: Activity H5 Project
 * @Date: 2025-01-01
 * @Description: 首页相关API接口
 */

import request from '@/utils/request';

// 获取首页Banner和精选活动信息
export const getHomePageList = (params = {}) => {
  return request({
    url: '/act-manage/homePage/getHomePageList',
    method: 'post',
    data: {
      page: 1,
      limit: 20,
      contentType: 1,
      status: 1,
      delFlag: '0',
      ...params
    }
  }).catch(error => {
    console.error('getHomePageList接口调用失败:', error);
    throw error;
  });
};

// 获取Banner轮播图
export const getBannerList = () => {
  return getHomePageList({
    buoyLocation: '1' // 1-头像位置
  });
};

// 获取精选活动
export const getFeaturedActivityList = () => {
  return getHomePageList({
    buoyLocation: '0' // 0-精选活动位置
  });
};

// 获取活动类型分类
export const getActivityCategoryList = (params = {}) => {
  return request({
    url: '/act-manage/activityType/getActivityType',
    method: 'post',
    data: {
      delFlag: '0',
      ...params
    }
  }).catch(error => {
    console.error('getActivityCategoryList接口调用失败:', error);
    throw error;
  });
};

// 获取活动列表（按类型）
export const getActivityList = (params = {}) => {
  return request({
    url: '/app/activity/getByTypeId',
    method: 'post',
    data: {
      typeId: null,
      popularity: 0,
      selectSort: 0,
      ...params
    }
  });
};

// 筛选活动列表
export const chooseActivity = (params = {}) => {
  return request({
    url: '/app/activity/chooseAct',
    method: 'post',
    data: {
      typeId: null,
      activityStatus: null,
      registerStatus: null,
      ...params
    }
  });
};

// 搜索活动
export const searchActivity = (params = {}) => {
  return request({
    url: '/app/activity/searchAct',
    method: 'post',
    data: {
      actId: null,
      actTitle: null,
      ...params
    }
  });
};

// 一键报名活动
export const registerActivity = (data) => {
  return request({
    url: '/app/activity/register',
    method: 'post',
    data: {
      id: null,
      name: null,
      phone: null,
      cardType: null,
      idCard: null,
      gender: null,
      age: null,
      human: null,
      child: null,
      high: null,
      weight: null,
      educate: null,
      community: null,
      address: null,
      selfAdds: null,
      ...data
    }
  });
};
